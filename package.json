{"name": "rapsap-api", "version": "1.2.3", "description": "The Core apis for customer mobile app and dashboard apis", "scripts": {"start": "./node_modules/pm2/bin/pm2-runtime api/index.js", "dev": "nodemon api/index.js", "poststart": "node ./node_modules/pm2/bin/pm2 logs", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@azure/storage-blob": "^12.28.0", "@pm2/io": "^6.1.0", "aws-sdk": "^2.917.0", "axios": "^0.21.1", "bcrypt": "^5.0.1", "body-parser": "^1.19.0", "cors": "^2.8.5", "csvtojson": "^2.0.10", "dotenv": "^10.0.0", "express": "^4.17.1", "firebase-admin": "^10.0.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "multer": "^1.4.2", "multiparty": "^4.2.2", "mysql2": "^2.2.5", "node-cron": "^4.2.0", "node-fetch": "^2.6.1", "pm2": "^5.3.0", "razorpay": "^2.0.6", "update": "^0.7.4", "uuid": "^8.3.2", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.10"}}
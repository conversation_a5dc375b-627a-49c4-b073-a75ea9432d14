-- WhatsApp Order System Database Updates
-- This script adds the order_source column to the orders table and updates existing procedures

-- Add order_source column to orders table
ALTER TABLE `orders` 
ADD COLUMN `order_source` VARCHAR(20) DEFAULT 'app' AFTER `order_rating`;

-- Update existing orders to have 'app' as order_source
UPDATE `orders` SET `order_source` = 'app' WHERE `order_source` IS NULL;

-- Add index for better performance on order_source queries
ALTER TABLE `orders` 
ADD INDEX `idx_orders_order_source` (`order_source`);

-- Update the spCreateOrder procedure to include order_source
DROP PROCEDURE IF EXISTS `spCreateOrderWithSource`;

DELIMITER ;;
CREATE PROCEDURE `spCreateOrderWithSource`(IN `p_data` JSON)
BEGIN
    DECLARE v_user_id INT DEFAULT NULL;
    DECLARE v_payment_id INT DEFAULT NULL;
    DECLARE v_address_id INT DEFAULT NULL;
    DECLARE v_offer_id INT DEFAULT NULL;
    DECLARE v_brand_id INT DEFAULT NULL;
    DECLARE v_is_subscription INT DEFAULT 0;
    DECLARE v_sub_total DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_gst DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_discount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_grand_total DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_store_id INT DEFAULT 1;
    DECLARE v_order_type VARCHAR(50) DEFAULT 'delivery';
    DECLARE v_delivery_cost DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_order_source VARCHAR(20) DEFAULT 'app';
    DECLARE v_order_id INT DEFAULT NULL;
    DECLARE v_orderDetails JSON;
    DECLARE v_product_id INT DEFAULT NULL;
    DECLARE v_variant_id INT DEFAULT NULL;
    DECLARE v_quantity INT DEFAULT NULL;
    DECLARE v_buying_price DECIMAL(10,2) DEFAULT NULL;
    DECLARE i INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION 
    BEGIN
        ROLLBACK;
        GET DIAGNOSTICS CONDITION 1
            @p1 = CLASS_ORIGIN,
            @p2 = SUBCLASS_ORIGIN,
            @p3 = RETURNED_SQLSTATE,
            @p4 = MESSAGE_TEXT,
            @p5 = MYSQL_ERRNO,
            @p6 = CONSTRAINT_CATALOG,
            @p7 = CONSTRAINT_SCHEMA,
            @p8 = CONSTRAINT_NAME,
            @p9 = CATALOG_NAME,
            @p10 = SCHEMA_NAME,
            @p11 = TABLE_NAME,
            @p12 = COLUMN_NAME,
            @p13 = CURSOR_NAME;
        SELECT 0 AS flag, @p4 AS msg;
    END;

    SET v_user_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.user_id'));
    SET v_payment_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.payment_id'));
    SET v_address_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.address_id'));
    SET v_offer_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.offer_id'));
    SET v_brand_id = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.brand_id'));
    SET v_is_subscription = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.is_subscription'));
    SET v_sub_total = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.sub_total'));
    SET v_gst = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.gst'));
    SET v_discount = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.discount'));
    SET v_grand_total = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.grand_total'));
    SET v_store_id = IFNULL(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.store_id')), 1);
    SET v_order_type = IFNULL(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.order_type')), 'delivery');
    SET v_delivery_cost = JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.delivery_cost'));
    SET v_order_source = IFNULL(JSON_UNQUOTE(JSON_EXTRACT(p_data, '$.order_source')), 'app');
    SET v_orderDetails = JSON_EXTRACT(p_data, '$.orderDetails');

    START TRANSACTION;

    INSERT INTO orders(user_id, status, payment_id, address_id, offer_id, brand_id, is_subscription, sub_total, gst, discount, grand_total, store_id, order_type, delivery_cost, order_source) 
    VALUES(v_user_id, 'order_created', v_payment_id, v_address_id, v_offer_id, v_brand_id, v_is_subscription, v_sub_total, v_gst, v_discount, v_grand_total, v_store_id, v_order_type, v_delivery_cost, v_order_source);
    
    SET v_order_id = LAST_INSERT_ID();
    
    WHILE i < JSON_LENGTH(v_orderDetails) DO
        SET v_product_id = JSON_EXTRACT(v_orderDetails, CONCAT('$[',i,'].product_id'));
        SET v_variant_id = JSON_EXTRACT(v_orderDetails, CONCAT('$[',i,'].variant_id'));      
        SET v_quantity = JSON_EXTRACT(v_orderDetails, CONCAT('$[',i,'].quantity'));
        SET v_buying_price = JSON_EXTRACT(v_orderDetails, CONCAT('$[',i,'].buying_price'));
        
        INSERT INTO order_details(order_id, product_id, variant_id, quantity, buying_price) 
        VALUES(v_order_id, v_product_id, v_variant_id, v_quantity, v_buying_price);
        
        SET i = i + 1;
    END WHILE;

    SELECT 1 AS flag, 'Order created successfully' AS msg, v_order_id AS order_id;
    
    COMMIT;
END;;
DELIMITER ;

const config = require('dotenv');
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const io = require('@pm2/io');
const packageJson = require('../package.json');

const userRoutes = require('./server/routes/UserRoutes');
const locationRoutes = require('./server/routes/LocationRoutes');
const EcommRoutes = require('./server/routes/EcommRoutes');
const DeliveryRoutes = require('./server/routes/DeliveryRoutes');
const DashboardRoutes = require('./server/routes/DashboardRoutes');
const NotificationRoutes = require('./server/routes/NotificationRoutes');
const MobileRoutes = require('./server/routes/MobileRoutes');
const WhatsAppOrderRoutes = require('./server/routes/WhatsAppOrderRoutes');
const { getGofrugalItems } = require('./server/controllers/EcommController/ProductController');
const serverActive = require('./cron/serverActive');

config.config();

const app = express();
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

const port = process.env.PORT || 4444;

app.use('/api/v1/mobile', userRoutes);
app.use('/api/v1/mobile', locationRoutes);
app.use('/api/v1/ecomm', EcommRoutes);
app.use('/api/v1/delivery', DeliveryRoutes);
app.use('/api/v1/dashboard', DashboardRoutes);
app.use('/api/v1/notification', NotificationRoutes);
app.use('/api/v2/mobile', MobileRoutes);
app.use('/api/v1/whatsapp', WhatsAppOrderRoutes);

// when a random route is inputed
app.get('*', (req, res) => res.status(200).send({
   message: `Welcome to Rapsap Customer & Dashboard API up and running... Version: ${packageJson.version}`
}));

app.listen(port, () => {
   console.log(`Server is running on PORT ${port} and version: ${packageJson.version}`);
   getGofrugalItems();
});

// Keep the server active
serverActive();

// PM2 metrics
const environment = process.env.NODE_ENV;
const version = packageJson.version;
const serverApp = process.env.PM2_SERVER_APP || 'rapsap-customer-api';

const appVersion = io.metric({
   name: 'appVersion',
   id: 'app:appVersion',
});
appVersion.set(`${version}`);

const appENV = io.metric({
   name: 'appENV',
   id: 'app:ENV',
});
appENV.set(`${environment}`);

const serverAppName = io.metric({
   name: 'serverAppName',
   id: 'app:serverAppName',
});
serverAppName.set(`${serverApp}`);

module.exports = app;
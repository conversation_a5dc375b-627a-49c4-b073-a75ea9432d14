const db = require('../../../config/connection');

const mobileLoadCartService = async (payload) => {
  try {
    const procedure = `CALL spMobileLoadCart(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetOffersService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetOffers(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileCreateOrderService = async (payload) => {
  try {
    const procedure = `CALL spMobileCreateOrder(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetOrdersService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetOrders(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetSingleOrderService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetSingleOrder(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetOngoingOrdersService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetOngoingOrders(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileCancelOrderService = async (payload) => {
  try {
    const procedure = `CALL spMobileCancelOrder(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

module.exports = {
  mobileLoadCartService,
  mobileGetOffersService,
  mobileCreateOrderService,
  mobileGetOrdersService,
  mobileGetSingleOrderService,
  mobileGetOngoingOrdersService,
  mobileCancelOrderService
};

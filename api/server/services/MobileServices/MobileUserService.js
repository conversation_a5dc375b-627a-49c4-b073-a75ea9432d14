const db = require('../../../config/connection');

const mobileSendOtpService = async (payload) => {
  try {
    const procedure = `CALL spMobileSendOtp(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
    throw error;
  }
};

const mobileVerifyOtpService = async (payload) => {
  try {
    const procedure = `CALL spMobileVerifyOtp(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
    throw error;
  }
};

const mobileRegisterUserService = async (payload) => {
  try {
    console.log("Payload in registerUser::>>", payload);
    const procedure = `CALL spMobileRegisterUser(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Raw results from registerUser procedure:", results);

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
    throw error;
  }
};

const mobileUpdateProfileService = async (payload) => {
  try {
    const procedure = `CALL spMobileUpdateProfile(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileCreateAddressService = async (payload) => {
  try {
    const procedure = `CALL spMobileCreateOrUpdateAddress(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetAddressesService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetAddresses(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileDeleteAddressService = async (payload) => {
  try {
    const procedure = `CALL spMobileDeleteAddress(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetNearbyStoreService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetNearbyStore(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

module.exports = {
  mobileSendOtpService,
  mobileVerifyOtpService,
  mobileRegisterUserService,
  mobileUpdateProfileService,
  mobileCreateAddressService,
  mobileGetAddressesService,
  mobileDeleteAddressService,
  mobileGetNearbyStoreService
};

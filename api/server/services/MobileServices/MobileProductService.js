const db = require('../../../config/connection');

const mobileGetProductByIdService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetProductById(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetItemsListService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetItemsList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetCategoriesService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetCategories(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetBannersService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetBanners(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetWishlistService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetWishlist(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileAddToWishlistService = async (payload) => {
  try {
    const procedure = `CALL spMobileAddToWishlist(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileRemoveFromWishlistService = async (payload) => {
  try {
    const procedure = `CALL spMobileRemoveFromWishlist(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

module.exports = {
  mobileGetProductByIdService,
  mobileGetItemsListService,
  mobileGetCategoriesService,
  mobileGetBannersService,
  mobileGetWishlistService,
  mobileAddToWishlistService,
  mobileRemoveFromWishlistService
};

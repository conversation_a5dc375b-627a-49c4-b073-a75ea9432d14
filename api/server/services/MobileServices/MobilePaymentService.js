const db = require('../../../config/connection');

const mobileInitiatePaymentService = async (payload) => {
  try {
    const procedure = `CALL spMobileInitiatePayment(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileCapturePaymentService = async (payload) => {
  try {
    const procedure = `CALL spMobileCapturePayment(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

const mobileGetPaymentStatusService = async (payload) => {
  try {
    const procedure = `CALL spMobileGetPaymentStatus(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw error;
  }
};

module.exports = {
  mobileInitiatePaymentService,
  mobileCapturePaymentService,
  mobileGetPaymentStatusService
};

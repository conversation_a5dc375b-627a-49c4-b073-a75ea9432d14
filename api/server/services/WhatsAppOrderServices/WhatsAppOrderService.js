const db = require('../../../config/connection');

const whatsappCreateOrderService = async (payload) => {
  try {
    const procedure = `CALL spWhatsAppCreateOrder(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in whatsappCreateOrderService", error);
    throw error;
  }
};

const whatsappUpdateOrderService = async (payload) => {
  try {
    const procedure = `CALL spWhatsAppUpdateOrder(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in whatsappUpdateOrderService", error);
    throw error;
  }
};

const whatsappGetOrderTrackingService = async (payload) => {
  try {
    const procedure = `CALL spWhatsAppGetOrderTracking(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in whatsappGetOrderTrackingService", error);
    throw error;
  }
};

module.exports = {
  whatsappCreateOrderService,
  whatsappUpdateOrderService,
  whatsappGetOrderTrackingService
};

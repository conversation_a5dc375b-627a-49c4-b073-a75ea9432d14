const db = require('../../../config/connection');

const whatsappGetAddressService = async (payload) => {
  try {
    const procedure = `CALL spWhatsAppGetAddress(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0]; // Return all addresses
    } else {
      return [];
    }
  } catch (error) {
    console.log("Error in whatsappGetAddressService", error);
    throw error;
  }
};

const whatsappCreateAddressService = async (payload) => {
  try {
    const procedure = `CALL spWhatsAppCreateAddress(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in whatsappCreateAddressService", error);
    throw error;
  }
};

const whatsappUpdateAddressService = async (payload) => {
  try {
    const procedure = `CALL spWhatsAppUpdateAddress(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in whatsappUpdateAddressService", error);
    throw error;
  }
};

module.exports = {
  whatsappGetAddressService,
  whatsappCreateAddressService,
  whatsappUpdateAddressService
};

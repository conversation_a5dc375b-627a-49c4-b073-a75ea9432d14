const db = require('../../../config/connection');

const whatsappGetProductsService = async (payload) => {
  try {
    const procedure = `CALL spWhatsAppGetProducts(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0]; // Return all products
    } else {
      return [];
    }
  } catch (error) {
    console.log("Error in whatsappGetProductsService", error);
    throw error;
  }
};

module.exports = {
  whatsappGetProductsService
};

const db = require('../../../config/connection');

const whatsappUserCreationService = async (payload) => {
  try {
    const procedure = `CALL spWhatsAppUserCreation(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in whatsappUserCreationService", error);
    throw error;
  }
};

const whatsappRegisterUserService = async (payload) => {
  try {
    const procedure = `CALL spWhatsAppRegisterUser(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in whatsappRegisterUserService", error);
    throw error;
  }
};

module.exports = {
  whatsappUserCreationService,
  whatsappRegisterUserService
};

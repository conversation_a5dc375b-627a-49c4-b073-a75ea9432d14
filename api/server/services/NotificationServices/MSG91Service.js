const axios = require('axios');

const MSG91_AUTH_KEY = process.env.MSG91_AUTH_KEY ;

const msg91OtpService = async (mobile, otp, templateId) => {
  try {
    if (process.env.NODE_ENV && process.env.NODE_ENV.toLowerCase().trim() !== 'production') {
      console.log(`OTP disabled in ${process.env.NODE_ENV} environment`);
      return { status: true, msg: 'OTP disabled in staging', data: {} };
    }
    
    // For OTP sending
    const otpConfig = {
      method: "post",
      url: `https://control.msg91.com/api/v5/otp`,
      headers: {
        "Content-Type": "application/json",
        "authkey": MSG91_AUTH_KEY
      },
      data: {
        mobile: `91${mobile}`,
        template_id: templateId,
        otp: otp,
        otp_expiry: 10 // In minutes
      }
    };

    const response = await axios(otpConfig);
    const responseData = response.data;
    console.log("MSG91 OTP Response:", responseData);

    if (responseData.type === "success") {
      return { status: true, msg: responseData.type, data: {} };
    } else {
      return { status: false, msg: "Failed to send OTP", data: responseData };
    }
  } catch (error) {
    console.log("MSG91 OTP Error:", error);
    return { status: false, msg: "Error sending OTP", data: {} };
  }
};

const msg91WhatsappService = async (mobile, templateName, bodyValues = {}) => {
  try {
    // Only work in production environment
    if (process.env.NODE_ENV && process.env.NODE_ENV.toLowerCase().trim() !== 'production') {
      console.log(`WhatsApp disabled in ${process.env.NODE_ENV} environment`);
      return { status: true, msg: 'WhatsApp disabled in staging', data: {} };
    }
    
    // Get OTP value from bodyValues
    const otpValue = bodyValues.OTP || bodyValues.otp || '';
    
    const whatsappData = {
      integrated_number: process.env.MSG91_WHATSAPP_NUMBER || "918530572636",
      content_type: "template",
      payload: {
        messaging_product: "whatsapp",
        type: "template",
        template: {
          name: templateName,
          language: {
            code: "en",
            policy: "deterministic"
          },
          namespace: process.env.MSG91_NAMESPACE || "78dd51ad_cb40_4786_b158_e43e2fc14a36",
          to_and_components: [
            {
              to: [`91${mobile}`],
              components: {
                body_1: {
                  type: "text",
                  value: otpValue
                },
                button_1: {
                  type: "text",
                  subtype: "url",
                  value: otpValue // Using same OTP value for button
                }
              }
            }
          ]
        }
      }
    };

    const options = {
      method: 'POST',
      url: 'https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/',
      headers: {
        'Content-Type': 'application/json',
        'authkey': MSG91_AUTH_KEY
      },
      data: whatsappData
    };

    console.log('WhatsApp Data:', JSON.stringify(whatsappData, null, 2));
    
    const response = await axios(options);
    console.log('WhatsApp Response:', response.data);
    
    return { 
      status: true, 
      msg: 'WhatsApp sent successfully', 
      data: response.data 
    };
  } catch (error) {
    console.error('WhatsApp Error:', error.response?.data || error.message);
    return { 
      status: false, 
      msg: 'Error sending WhatsApp', 
      data: error.response?.data || {} 
    };
  }
};

module.exports = { msg91OtpService, msg91WhatsappService };

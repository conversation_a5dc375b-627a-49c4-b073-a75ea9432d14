const db = require('../../../config/connection');
const { admin } = require('../../../config/firebase-config');
const axios = require('axios');
const MSG91_AUTH_KEY = process.env.MSG91_AUTH_KEY ;



  const getnotificationService = async (payload) => {
  const { receiver_id, data } = payload;
  console.log("payload", payload);
  try {
    const sql = `select firebase_id as fire_base_id from users where id = ?`;
    const [[row]] = await db.promise().query(sql, receiver_id);
    if (row) {
      console.log("row", row.fire_base_id);
      const message = {
        data: data,
        apns: {
          headers: {
            "apns-priority": "10"
          },
          payload: {
            aps: {
              badge: 0,
              sound: "default",
              alert: {
                title: data.title,
                body: data.body,
                type: data.type,
              },
            },
            order_id: data.order_id,
          },
          fcm_options: {
            image: ''}
        },
        token: row.fire_base_id,
      };
      const msg = await admin.messaging().send(message);
          if (msg) {
            return msg
          } else {
            return null;
          }
    }
  } catch (error) {
    throw new Error(error);
  }
}

  const getDynamicLink = async (payload) => {
  const suffix = payload;
  console.log('payload in notification', payload);
  try {
        const 
        payloadData={
      "longDynamicLink": `https://rapsap.page.link/?link=https://rapsap.com/${suffix}&apn=consumer.rapsap.com&isi=6443778088&ibi=com.rapsap.app`,
      "suffix": {
        "option": "SHORT"
      }
      }
      const options = {
        'method': 'POST',
        'url': `https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=${process.env.WEB_API_KEY}`,
        'headers': {
          
          'Content-Type': 'application/json'
        },
        data: payloadData
      };

      const result = await axios(options);
      console.log('result ', result);
      // console.log('result', result.data);



      // if (result.data.status === "ACCEPTED") {
        return result.data
      // } 
      // else {
      //   return null
      // }
    } catch (error) {
      console.log('error', error);
    }
  }

  const getWhatsappService = async (payload) => {
  const { user_id, data, type, template_name, body_values } = payload;
  console.log('payload in notification', payload);
  try {
      const temp = `select first_name as user_name, mobile,(select b.is_prod from brand b where b.id = 1) as is_prod from temp_user where id = ?`;
      const user = `select first_name as user_name, mobile,(select b.is_prod from brand b where b.id = 1) as is_prod from users where id = ?`;
      const sql = (type == "temp" ? temp : user);
      const [[row]] = await db.promise().query(sql, user_id);
      const name = data.name != null ? data.name : row.user_name;
      let payloadData = {};
      if (row.is_prod == 0) {
        payloadData = {
          "channelId": process.env.RAPSAP_GALLABOX_CHANNEL_ID,
          "channelType": "whatsapp",
          "recipient": {
            "name": name || "Rapsap User",
            "phone": data.mobile != null ? `91${data.mobile}` : `91${row.mobile}`
          },
          "whatsapp": {
            "type": "text",
            "text": {
              "body": data.message
            }
          }
        };
      } else {
        payloadData = {
          "channelId": process.env.RAPSAP_GALLABOX_CHANNEL_ID,
          "channelType": "whatsapp",
          "recipient": {
            "name": name || "Rapsap User",
            "phone": data.mobile != null ? `91${data.mobile}` : `91${row.mobile}`
          },
          "whatsapp": {
            "type": "template",
            "template": {
              "templateName": template_name,
              "bodyValues": body_values
            }
          }
        };
      }
      console.log('payloadData', payloadData);
      const options = {
        'method': 'POST',
        'url': `https://server.gallabox.com/devapi/messages/whatsapp`,
        'headers': {
          'apiSecret': process.env.RAPSAP_GALLABOX_API_SECRET,
          'apiKey': process.env.RAPSAP_GALLABOX_API_KEY,
          'Content-Type': 'application/json'
        },
        data: JSON.stringify(payloadData)
      };

      const result = await axios(options);
      console.log('result', result.data);

      if (result.data.status === "ACCEPTED") {
        return { status: true, msg: result.data.message, data: {} };
      } else {
        return null
      }
    } catch (error) {
      console.log('error', error);
    }
  }

  const CallWebhook = async (payload) => {
    const sql = `select webhookurl from webhook where brand_id =1`;

    const [[{webhookurl}]] = await db.promise().query(sql);
    const options = {
      'method': 'POST',
      'url': webhookurl,
      'headers': {
        'Content-Type': 'application/json'
      },
      data: JSON.stringify(payload)
    };
    return await axios(options);
  }

  const msg91OtpService = async (mobile, otp, templateId) => {
    try {
      if (process.env.NODE_ENV && process.env.NODE_ENV.toLowerCase().trim() !== 'production') {
        console.log(`OTP disabled in ${process.env.NODE_ENV} environment`);
        return { status: true, msg: 'OTP disabled in staging', data: {} };
      }
      
      // For OTP sending
      const otpConfig = {
        method: "post",
        url: `https://control.msg91.com/api/v5/otp`,
        headers: {
          "Content-Type": "application/json",
          "authkey": MSG91_AUTH_KEY
        },
        data: {
          mobile: `91${mobile}`,
          template_id: templateId,
          otp: otp,
          otp_expiry: 10 // In minutes
        }
      };
  
      const response = await axios(otpConfig);
      const responseData = response.data;
      console.log("MSG91 OTP Response:", responseData);
  
      if (responseData.type === "success") {
        return { status: true, msg: responseData.type, data: {} };
      } else {
        return { status: false, msg: "Failed to send OTP", data: responseData };
      }
    } catch (error) {
      console.log("MSG91 OTP Error:", error);
      return { status: false, msg: "Error sending OTP", data: {} };
    }
  };
  
  const msg91WhatsappService = async (mobile, templateName, bodyValues = {}) => {
    try {
      // Only work in production environment
      if (process.env.NODE_ENV && process.env.NODE_ENV.toLowerCase().trim() !== 'production') {
        console.log(`WhatsApp disabled in ${process.env.NODE_ENV} environment`);
        return { status: true, msg: 'WhatsApp disabled in staging', data: {} };
      }
      
      // Get OTP value from bodyValues
      const otpValue = bodyValues.OTP || bodyValues.otp || '';
      
      const whatsappData = {
        integrated_number: process.env.MSG91_WHATSAPP_NUMBER || "918530572636",
        content_type: "template",
        payload: {
          messaging_product: "whatsapp",
          type: "template",
          template: {
            name: templateName,
            language: {
              code: "en",
              policy: "deterministic"
            },
            namespace: process.env.MSG91_NAMESPACE || "78dd51ad_cb40_4786_b158_e43e2fc14a36",
            to_and_components: [
              {
                to: [`91${mobile}`],
                components: {
                  body_1: {
                    type: "text",
                    value: otpValue
                  },
                  button_1: {
                    type: "text",
                    subtype: "url",
                    value: otpValue // Using same OTP value for button
                  }
                }
              }
            ]
          }
        }
      };
  
      const options = {
        method: 'POST',
        url: 'https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/',
        headers: {
          'Content-Type': 'application/json',
          'authkey': MSG91_AUTH_KEY
        },
        data: whatsappData
      };
  
      console.log('WhatsApp Data:', JSON.stringify(whatsappData, null, 2));
      
      const response = await axios(options);
      console.log('WhatsApp Response:', response.data);
      
      return { 
        status: true, 
        msg: 'WhatsApp sent successfully', 
        data: response.data 
      };
    } catch (error) {
      console.error('WhatsApp Error:', error.response?.data || error.message);
      return { 
        status: false, 
        msg: 'Error sending WhatsApp', 
        data: error.response?.data || {} 
      };
    }
  };


module.exports = { getnotificationService, getWhatsappService, CallWebhook, getDynamicLink, msg91OtpService, msg91WhatsappService };
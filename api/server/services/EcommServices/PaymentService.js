const db = require('../../../config/connection');

const initiatePaymentService = async (payload) => {
  try {
    //const procedure = `CALL spInitiatePayment(?)`;
    const procedure = `CALL spInitiatePaymentByInvoice(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const refundPaymentService = async (payload) => {
  try {
    const procedure = `CALL spRefundPayment(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}


const capturedPaymentService = async (payload) => {
  try {
    const procedure = `CALL spCapturedPayment(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const PaymentListService = async (payload) => {
  try {
    const procedure = `CALL spGetPaymentList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const initiateInvoiceService = async (payload) => {
  try {
    const procedure = `CALL spInitiateInvoice(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const getPaymentKeys = async () => {
  try {
    const procedure = `CALL spGetPaymentKeys()`;
    const [row] = await db.promise().query(procedure);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const PaymentByIDService = async (payload) => {
  try {
    const procedure = `CALL spPaymentByID(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

module.exports = { initiatePaymentService, capturedPaymentService, PaymentListService, initiateInvoiceService, getPaymentKeys, PaymentByIDService , refundPaymentService};
const db = require('../../../config/connection');
const { getgofrugalkeys } = require('./GofrugalService');
const axios = require('axios');


const createOrderService = async (payload) => {
  try {
    const procedure = `CALL spCreateOrder(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const updateCartService = async (payload) => {
  try {
    const procedure = `CALL spUpdateCart(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }

}

const getcartService = async (payload) => {
  try {
    const procedure = `CALL spgetCart(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const getOrderService = async (payload) => {
  try {
    const procedure = `CALL spOrderList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const getOrderByIDService = async (payload) => {
  try {
    const procedure = `CALL spOrderByID(?)`;
    const [[row, second]] = await db.promise().query(procedure, JSON.stringify(payload));
    return second[0].total > 0 ? row[0] : {};
  } catch (error) {
    throw new Error(error);
  }
}

const updateOrderService = async (payload) => {
  try {
    const procedure = `CALL spUpdateOrder(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const cancelOrderService = async (payload) => {
  try {
    const procedure = `CALL spCancelOrder(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const orderStatusService = async (payload) => {
  try {
    const procedure = `CALL spOrderStatus(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

const updateGofrugalOrderService=async (payload)=> {
   
  try {



    const{mode,order_id,status}=payload;
let customFilter="";

  const [[{domainpath,token}]]=await getgofrugalkeys();
  const orderpayload={
    'order_id':order_id,
  }
  if(mode==="edit"){

    customFilter="?mode=edit"
  }
  if(mode==="cancel"){
    customFilter="?mode=cancel"

  }


  
  const data =  await   getOrderByIDService(orderpayload);
  console.log('data :>> ', data);

  let gofrugalpayload={
    "salesOrder": {
   
   "OrderDiscAmt":parseFloat(data.discount),
   "orderDiscPerc":0.0,
    "onlineReferenceNo": `${data.order_id}`,
    "createdAt": `${data.created_at.toISOString()}`,
    "totalQuantity":parseFloat(data.total_qty),
    "totalAmount": data.grand_total,
    "totalTaxAmount": "0.0",
    "totalDiscountAmount": parseFloat(data.discount),
    "shippingId": data.address_id.toString(),
    "shippingName": data.delivery_address.name,
    "shippingAddress1": data.delivery_address.address1,
    "shippingAddress2": data.delivery_address.address2,
    "shippingPlace":data.delivery_address.city,
    "shippingState": data.delivery_address.state,
    "shippingCountry": "India",
    "shippingPincode": data.delivery_address.pincode,
    "shippingMobile": data.delivery_address.phone,
    "outletId": data.store_id,
    "shippingCharge": data.delivery_cost?data.delivery_cost.toString():"0.00",
    "packingCharge": "0.0",

    "shipmentItems": data.OrderDetails.length,
    "orderRemarks": "",
    "customerName": data.delivery_address.name,
    "customerAddressLine1": data.delivery_address.address1,
    "customerAddressLine2": data.delivery_address.address2,
    "customerArea": data.delivery_address.city,
    "customerState": data.delivery_address.state,
    "customerCountry":"India",
    "customerPincode": data.delivery_address.pincode,
    "customerMobile": data.delivery_address.phone,
    "customerPhone": data.delivery_address.phone,
    "customerEmail": data.email,
    "paymentMode":data.payment_type,
    "orderItems":data.OrderDetails.map((e,index)=>{
               
return   {"rowNo": index+1,
      "itemId": e.ref_id.toString(),
      "itemReferenceCode": e.ref_id.toString(),
      "salePrice": `${e.buying_price}`,
      "quantity": `${e.quantity}`,
      "itemAmount": parseFloat(e.buying_price),
      "taxPercentage": "0.0",
      "itemMarketPrice": `${e.mrp}`
    };
    })

  
       }
   }


   if(status==="delivered"){
    gofrugalpayload.salesOrder.status = "delivered";

    
   }
   else 
   if(status==="packed"){
    gofrugalpayload.salesOrder.status = "Order punched";

    

   }else
   if(status==="out_for_delivery"){
    gofrugalpayload.salesOrder.status = "Out for Delivery";

    
   }
   
   



    const options = {
      'method': 'POST',

      'url': `${domainpath}/api/v1/salesOrders${customFilter}`,
      'headers': {
        
        'Content-Type': 'application/json',
        'X-Auth-Token':token
      },


      data:gofrugalpayload
    };

    console.log('data :>> ', options);


    const result = await axios(options);
    console.log('result ', result.data.result.id);

    if(result && !mode){
      if(result.data.result.status==="success"){
        await saveOrderPosRef({newPosOrderRef:result.data.result.id,
          orderId:data.order_id
          
         })

        
      }



    }
    


  return true;


    
  } catch (error) {
    console.log('error', error);

    throw new Error(error);

  }
}

const saveOrderPosRef = async (payload) => {
  const { newPosOrderRef, orderId } = payload;
  try {
    const sql = `UPDATE orders SET pos_order_ref = ? WHERE id = ?`;
    const [row] = await db.promise().query(sql, [newPosOrderRef, orderId]);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
}

module.exports = { createOrderService, getcartService, updateCartService, getOrderService, getOrderByIDService,updateOrderService,cancelOrderService,orderStatusService,updateGofrugalOrderService };
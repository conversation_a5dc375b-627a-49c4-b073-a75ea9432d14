const db = require('../../../config/connection');

const totalUsersService = async (payload) => {
    const { type } = payload;
    try {
        let where = '';
        if (type == 'month') {
            where = `DATE(u.create_at) >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)  and DATE(u.create_at) <= CURDATE()`;
        } else {
            where = `DATE(u.create_at) >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)  and DATE(u.create_at) <= CURDATE()`;
        }
        const sql = `select count(u.id) as total_users from users u
        where u.role_id = 3 and ${where}`;
        const [[users]] = await db.promise().query(sql);

        const weekly = `SELECT
        (DATE(NOW()) - INTERVAL day DAY) AS DayDate,
        COUNT(id) AS count
        FROM (
            SELECT 0 AS day
            UNION SELECT 1
            UNION SELECT 2
            UNION SELECT 3
            UNION SELECT 4
            UNION SELECT 5
            UNION SELECT 6
        ) AS week
        LEFT JOIN users u ON DATE(u.create_at) = (DATE(NOW()) - INTERVAL day DAY)
        GROUP BY DayDate
        ORDER BY DayDate ASC;`;

        // const monthly = `CALL spGetOrdersChart(?)`;
        let row = ``;
        let result = [];
        if (type == "month") {
            [[row]] = await db.promise().query(monthly, JSON.stringify(payload));
            console.log('row', row);
            for (let value of Object.values(row)) {
                console.log('value', value.count);
                result.push(value.count);
            }
        } else {
            row = await db.promise().query(weekly);
            console.log('row', row[0]);
            for (let value of Object.values(row[0])) {
                console.log('value', value.count);
                result.push(value.count);
            }
        }
        console.log('result', result);
        const usersobj = { linegraph: result || [], total_users: users.total_users || 0 }
        if (result) {
            return usersobj;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const dashboardCountsService = async (payload) => {
    console.log('payload', payload);
    try {
        // Call the new stored procedure
        const procedure = `CALL spGetDashboardCounts(?)`;
        const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
        
        // console.log('Dashboard counts result:', row);
        const data = row[0] || {};

        if (data && Object.keys(data).length > 0) {
            return {
                total_orders: data.total_orders || 0,
                total_revenue: data.total_revenue || "0.00",
                avg_order_value: parseFloat(data.avg_order_value || 0).toFixed(2),
                total_customers: data.total_customers || 0,
                app_users: data.app_users || 0,
                total_products: data.total_products || 0
            };
        } else {
            return {
                total_orders: 0,
                total_revenue: "0.00",
                avg_order_value: "0.00",
                total_customers: 0,
                app_users: 0,
                total_products: 0
            };
        }
    } catch (error) {
        throw new Error(error);
    }
}

const OrdersChartService = async (payload) => {
    try {
        const procedure = `CALL spGetOrdersChart(?)`;
        const [results] = await db.promise().query(procedure, JSON.stringify(payload));
        
        // console.log('Orders chart results:', results);
        
        let piechart = [];
        let linegraph = [];
        
        // Process results based on actual structure
        if (results && results.length >= 3) {
            // Process piechart data (second result set - index 1)
            if (results[1] && results[1].length > 0) {
                piechart = results[1].map(row => ({
                    type: row.type,
                    value: row.value,
                    percentage: parseFloat(row.percentage)
                }));
            }
            
            // Process linegraph data (third result set - index 2)  
            if (results[2] && results[2].length > 0) {
                linegraph = results[2].map(row => ({
                    time: row.time,
                    orders: row.orders,
                    sales: parseInt(row.sales)
                }));
            }
        }
        
        // Ensure piechart has both types if missing
        const types = ['in_store', 'delivery'];
        const existingTypes = piechart.map(item => item.type);
        
        types.forEach(type => {
            if (!existingTypes.includes(type)) {
                piechart.push({
                    type: type,
                    value: 0,
                    percentage: 0
                });
            }
        });
        
        // Sort piechart to have consistent order (in_store first)
        piechart.sort((a, b) => a.type === 'in_store' ? -1 : 1);
        
        const result = {
            piechart: piechart,
            linegraph: linegraph
        };
        
        console.log('Processed result:', result);
        
        return result;
    } catch (error) {
        console.log('Error in OrdersChartService:', error);
        throw new Error(error);
    }
}

const registerWebhookService = async (payload) => {
    try {
        const procedure = `call spregisterWebhook(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(payload));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const getWebhookService = async (payload) => {
    try {
        const procedure = `select id,webhookurl,events,CAST(is_active AS SIGNED) as is_active, updated_at  from webhook where brand_id=1`;
        const [row] = await db.promise().query(procedure);
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const getStoresService = async (keyword) => {
    try {
        const procedure = `call spGetStores(?)`;
        const [row] = await db.promise().query(procedure, JSON.stringify(keyword));
        if (row) {
            return row;
        } else {
            return null;
        }
    } catch (error) {
        throw new Error(error);
    }
}

const getTopProductsService = async (payload) => {
    try {
        const procedure = `CALL spGetTopProducts(?)`;
        const [results] = await db.promise().query(procedure, JSON.stringify(payload));
        
        console.log('Top products results:', results);
        
        if (results && results[0] && results[0].length > 0) {
            return results[0].map(row => ({
                image_url: row.image_url || '',
                product_name: row.product_name || '',
                sku: row.sku || '',
                value: parseFloat(row.value || 0),
                currency: row.currency || 'INR',
                category: row.category || '',
                orders_count: parseInt(row.orders_count || 0)
            }));
        } else {
            return [];
        }
    } catch (error) {
        console.log('Error in getTopProductsService:', error);
        throw new Error(error);
    }
}

module.exports = { totalUsersService, OrdersChartService, dashboardCountsService,getStoresService, registerWebhookService,getWebhookService,getTopProductsService }
const db = require("../../../config/connection");
// const { calcKmDistanceFn } = require('../../utility/distanceCal');

const checklatlongService = async (payload) => {
  try {
    const { pincode, lat, long } = payload;

    let sqlQuery = `select  ST_Distance_Sphere( point (${lat}, ${long}),  point(lat, lng)) * 0.001
            as distance_in_km, s.code as store_code, s.id as store_id, s.name as store_name, s.radius, s.contact_no as store_contact, (select distance_in_km < s.radius) as 'isFast'
            from store s
            order by distance_in_km asc
            limit 1;
        `;

    const [[pin]] = await db.promise().query(
      `select ss.id as store_id, ss.name as store_name, ss.code as store_code , ss.contact_no from pincode p
            join store ss on p.store_id = ss.id
            where p.pincode = ? ;`,
      pincode
    );

    const [[row]] = await db.promise().query(sqlQuery);

    // console.log('row', row);
    // console.log('pin', pin);

    if (row) {
      return { row: row, pin: pin };
    } else {
      return null;
    }
  } catch (error) {
    throw new Error(error);
  }
};

module.exports = { checklatlongService };

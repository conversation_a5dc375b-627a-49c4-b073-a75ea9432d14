const { whatsappUserCreationService, whatsappRegisterUserService } = require('../../services/WhatsAppOrderServices/WhatsAppUserService');
const { requiredParams } = require('../../utility/requiredCheck');
const jwt = require('jsonwebtoken');
const db = require('../../../config/connection');

class WhatsAppUserController {
  
  static async whatsappUserCreation(req, res) {
    try {
      const payload = req.body;
      const { mobile } = payload;

      if (!mobile) return requiredParams(res, 'mobile is required!');

      if (!/^\d{10}$/.test(mobile)) {
        return res.status(400).send({
          success: false,
          msg: "Mobile number must be 10 digits long",
          data: {},
        });
      }

      const result = await whatsappUserCreationService(payload);

      if (result && result.flag === 1) {
        // Check if user exists and is verified
        if (result.user_id && result.otp_verified === 1) {
          // Existing verified user - return login response
          const token = jwt.sign({ result: mobile }, process.env.JWT_KEY, {
            expiresIn: "30d",
          });

          return res.status(200).send({
            success: true,
            msg: "User verified successfully",
            data: {
              user_id: result.user_id,
              mobile: mobile,
              first_name: result.first_name || 'WhatsApp User',
              is_newuser: 0,
              token: token
            }
          });
        } else {
          // New user - return new user response
          return res.status(200).send({
            success: true,
            msg: "New user detected",
            data: {
              mobile: mobile,
              is_newuser: 1,
              temp_id: result.temp_id || null
            }
          });
        }
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'User creation failed',
          data: {}
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }

  static async registerUser(req, res) {
    try {
      const payload = req.body;
      const { mobile, first_name, last_name, email } = payload;

      if (!mobile) return requiredParams(res, 'mobile is required!');
      if (!first_name) return requiredParams(res, 'first_name is required!');

      // Check if user already exists
      const checkSQL = `SELECT count(*) as status FROM users WHERE mobile = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [mobile]);
      
      if (isExist.status > 0) {
        return res.status(400).send({
          success: false,
          msg: "User already exists",
          data: {}
        });
      }

      const result = await whatsappRegisterUserService(payload);

      if (result && result.flag === 1) {
        const token = jwt.sign({ result: mobile }, process.env.JWT_KEY, {
          expiresIn: "30d",
        });

        return res.status(200).send({
          success: true,
          msg: "User registration successful",
          data: {
            user_id: result.user_id,
            mobile: mobile,
            first_name: first_name,
            last_name: last_name || null,
            email: email || null,
            is_newuser: 0,
            token: token
          }
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'User registration failed',
          data: {}
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }
}

module.exports = WhatsAppUserController;

const { whatsappGetAddressService, whatsappCreateAddressService, whatsappUpdateAddressService } = require('../../services/WhatsAppOrderServices/WhatsAppAddressService');
const { requiredParams } = require('../../utility/requiredCheck');

class WhatsAppAddressController {
  
  static async getAddress(req, res) {
    try {
      const payload = req.body;
      const { user_id, page = 1, limit = 10 } = payload;

      if (!user_id) return requiredParams(res, 'user_id is required!');

      const addressPayload = { 
        user_id, 
        page: parseInt(page), 
        limit: parseInt(limit) 
      };

      const result = await whatsappGetAddressService(addressPayload);

      if (result && result.length > 0) {
        return res.status(200).send({
          success: true,
          msg: "Addresses retrieved successfully",
          data: {
            docs: result,
            totalDocs: result.length,
            page: parseInt(page),
            limit: parseInt(limit)
          }
        });
      } else {
        return res.status(200).send({
          success: true,
          msg: "No addresses found",
          data: {
            docs: [],
            totalDocs: 0,
            page: parseInt(page),
            limit: parseInt(limit)
          }
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {
          docs: []
        }
      });
    }
  }

  static async createAddress(req, res) {
    try {
      const payload = req.body;
      const { user_id, name, phone, address_type, address1, address2, landmark, state, city, pincode, latitude, longitude } = payload;

      if (!user_id) return requiredParams(res, 'user_id is required!');
      if (!name) return requiredParams(res, 'name is required!');
      if (!phone) return requiredParams(res, 'phone is required!');
      if (!address_type) return requiredParams(res, 'address_type is required!');
      if (!address1) return requiredParams(res, 'address1 is required!');
      if (!city) return requiredParams(res, 'city is required!');
      if (!pincode) return requiredParams(res, 'pincode is required!');
      if (!latitude || !longitude) {
        return requiredParams(res, 'latitude and longitude are required!');
      }

      const result = await whatsappCreateAddressService(payload);

      if (result && result.flag === 1) {
        return res.status(200).send({
          success: true,
          msg: result.msg || "Address created successfully",
          data: {
            address_id: result.address_id
          }
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Address creation failed',
          data: {}
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }

  static async updateAddress(req, res) {
    try {
      const payload = req.body;
      const { address_id, user_id } = payload;

      if (!address_id) return requiredParams(res, 'address_id is required!');
      if (!user_id) return requiredParams(res, 'user_id is required!');

      const result = await whatsappUpdateAddressService(payload);

      if (result && result.flag === 1) {
        return res.status(200).send({
          success: true,
          msg: result.msg || "Address updated successfully",
          data: {}
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Address update failed',
          data: {}
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }
}

module.exports = WhatsAppAddressController;

const { whatsappCreateOrderService, whatsappUpdateOrderService, whatsappGetOrderTrackingService } = require('../../services/WhatsAppOrderServices/WhatsAppOrderService');
const { msg91WhatsappService } = require('../../services/NotificationServices/MSG91Service');
const { requiredParams } = require('../../utility/requiredCheck');

class WhatsAppOrderController {
  
  static async createOrder(req, res) {
    try {
      const payload = req.body;
      const { user_id, items, address_id, store_id = 1, payment_method = 'COD' } = payload;

      if (!user_id) return requiredParams(res, 'user_id is required!');
      if (!items || !Array.isArray(items) || items.length === 0) {
        return requiredParams(res, 'items array is required!');
      }
      if (!address_id) return requiredParams(res, 'address_id is required!');

      // Validate items structure
      for (let item of items) {
        if (!item.ref_id) return requiredParams(res, 'ref_id is required for each item!');
        if (!item.quantity || item.quantity <= 0) return requiredParams(res, 'valid quantity is required for each item!');
      }

      const orderPayload = {
        ...payload,
        store_id: parseInt(store_id),
        payment_method,
        order_source: 'whatsapp'
      };

      const result = await whatsappCreateOrderService(orderPayload);

      if (result && result.flag === 1) {
        // Send WhatsApp notification for order confirmation
        try {
          await msg91WhatsappService(
            result.mobile, 
            "order_confirmation", 
            { 
              ORDER_ID: result.order_id,
              TOTAL_AMOUNT: result.grand_total 
            }
          );
        } catch (notificationError) {
          console.log("WhatsApp notification error:", notificationError);
          // Don't fail the order creation if notification fails
        }

        return res.status(200).send({
          success: true,
          msg: result.msg || "Order created successfully",
          data: {
            order_id: result.order_id,
            grand_total: result.grand_total,
            status: result.status || 'order_created'
          }
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Order creation failed',
          data: {}
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }

  static async updateOrder(req, res) {
    try {
      const payload = req.body;
      const { order_id, status, updated_by } = payload;

      if (!order_id) return requiredParams(res, 'order_id is required!');
      if (!status) return requiredParams(res, 'status is required!');

      const updatePayload = {
        ...payload,
        order_source: 'whatsapp'
      };

      const result = await whatsappUpdateOrderService(updatePayload);

      if (result && result.flag === 1) {
        // Send WhatsApp notification for order status update
        try {
          if (result.mobile) {
            await msg91WhatsappService(
              result.mobile, 
              "order_status_update", 
              { 
                ORDER_ID: order_id,
                STATUS: status 
              }
            );
          }
        } catch (notificationError) {
          console.log("WhatsApp notification error:", notificationError);
          // Don't fail the order update if notification fails
        }

        return res.status(200).send({
          success: true,
          msg: result.msg || "Order updated successfully",
          data: {
            order_id: order_id,
            status: status
          }
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Order update failed',
          data: {}
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }

  static async getOrderTracking(req, res) {
    try {
      const payload = req.body;
      const { order_id, user_id } = payload;

      if (!order_id) return requiredParams(res, 'order_id is required!');

      const result = await whatsappGetOrderTrackingService(payload);

      if (result && result.order_id) {
        return res.status(200).send({
          success: true,
          msg: "Order tracking details retrieved successfully",
          data: result
        });
      } else {
        return res.status(404).send({
          success: false,
          msg: "Order not found",
          data: {}
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }
}

module.exports = WhatsAppOrderController;

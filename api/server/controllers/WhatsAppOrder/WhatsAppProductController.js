const { whatsappGetProductsService } = require('../../services/WhatsAppOrderServices/WhatsAppProductService');
const { requiredParams } = require('../../utility/requiredCheck');

class WhatsAppProductController {
  
  static async getProducts(req, res) {
    try {
      const payload = req.body;
      const { search_term, ref_id, store_id = 1, page = 1, limit = 10 } = payload;

      if (!search_term && !ref_id) {
        return requiredParams(res, 'search_term or ref_id is required!');
      }

      const productPayload = {
        search_term,
        ref_id,
        store_id: parseInt(store_id),
        page: parseInt(page),
        limit: parseInt(limit)
      };

      const result = await whatsappGetProductsService(productPayload);

      if (result && result.length > 0) {
        return res.status(200).send({
          success: true,
          msg: "Products retrieved successfully",
          data: {
            docs: result,
            totalDocs: result.length,
            page: parseInt(page),
            limit: parseInt(limit)
          }
        });
      } else {
        return res.status(200).send({
          success: true,
          msg: "No products found",
          data: {
            docs: [],
            totalDocs: 0,
            page: parseInt(page),
            limit: parseInt(limit)
          }
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {
          docs: []
        }
      });
    }
  }
}

module.exports = WhatsAppProductController;

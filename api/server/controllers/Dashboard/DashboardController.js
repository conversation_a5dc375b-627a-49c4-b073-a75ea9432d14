const {
  totalUsersService,
  OrdersChartService,
  dashboardCountsService,
  getStoresService,
  getTopProductsService,
} = require("../../services/DashboardServices/DashboardService");
const {
  getgofrugalkeys,
} = require("../../services/EcommServices/GofrugalService");
const axios = require("axios");
const { convertDateFormat } = require("../../utility/dateformat");

class DashboardController {
  static async totalUsers(req, res) {
    const payload = req.body;
    const result = await totalUsersService(payload);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: " Total Users",
          data: result,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async dashboardCounts(req, res) {
    const payload = req.body;

    try {
      const result = await dashboardCountsService(payload);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Dashboard counts retrieved successfully",
          data: {
            total_orders: result.total_orders,
            total_revenue: result.total_revenue,
            avg_order_value: result.avg_order_value,
            total_customers: result.total_customers,
            app_users: result.app_users,
            total_products: result.total_products,
          },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async OrdersChart(req, res) {
    const payload = req.body;

    try {
      const result = await OrdersChartService(payload);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Orders chart data",
          data: {
            piechart: result.piechart || [],
            linegraph: result.linegraph || [],
          },
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "No data found",
          data: {
            piechart: [],
            linegraph: [],
          },
        });
      }
    } catch (error) {
      console.log("Error in OrdersChart controller:", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {
          piechart: [],
          linegraph: [],
        },
      });
    }
  }

  static async getStores(req, res) {
    const payload = req.body;

    try {
      const [result, meta] = await getStoresService(payload);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Store list" : "No Stores",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getGofrugalPurchase(req, res) {
    const payload = req.body;

    try {
      const [[{ domainpath, token }]] = await getgofrugalkeys();
      const { start_date, end_date, page, size, sort } = payload;
      let customFilter = "";

      if (start_date && end_date) {
        customFilter = `?q=PoDate>=${convertDateFormat(
          start_date,
          "start"
        )},PoDate<=${convertDateFormat(end_date, "end")}`;
      }

      if (page) {
        customFilter += customFilter
          ? `&page=${page + 1}`
          : `?page=${page + 1}`;
      }

      if (size) {
        customFilter += customFilter ? `&limit=${size}` : `?limit=${size}`;
      }

      if (sort?.field === "createdAt" && sort?.order_by === "desc") {
        customFilter += customFilter
          ? `&order=${sort?.field}`
          : `?order==${sort?.field}`;
      }

      const options = {
        method: "GET",
        url: `${domainpath}/api/v1/purchaseOrders${customFilter}`,
        headers: {
          "Content-Type": "application/json",
          "X-Auth-Token": token,
        },
      };

      const result = await axios(options);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: !result.data.purchaseOrders
            ? "No purchase found"
            : "Gofrugal Purchase",
          data: !result.data.purchaseOrders ? [] : result.data.purchaseOrders,
          meta: {
            total: result.data.total_records,
          },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getTopProducts(req, res) {
    const payload = req.body;

    try {
      const result = await getTopProductsService(payload);

      return res.status(200).send({
        success: true,
        msg: "Top 5 products",
        data: result || [],
      });
    } catch (error) {
      console.log("Error in getTopProducts controller:", error);
      return res.status(200).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: [],
      });
    }
  }
}

module.exports = DashboardController;

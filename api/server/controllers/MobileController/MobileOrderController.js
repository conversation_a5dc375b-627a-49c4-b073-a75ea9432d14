const { mobileLoadCartService, mobileGetOffersService, mobileCreateOrderService, mobileGetOrdersService, mobileGetSingleOrderService, mobileGetOngoingOrdersService, mobileCancelOrderService } = require('../../services/MobileServices/MobileOrderService');
const { requiredParams } = require('../../utility/requiredCheck');

class MobileOrderController {
  
  static async loadCart(req, res) {
    try {
      const payload = req.body;
      const { store, items } = payload;
      
      if (!store) return requiredParams(res, 'store is required!');
      if (!items || !Array.isArray(items) || items.length === 0) {
        return requiredParams(res, 'items array is required!');
      }
      
      const result = await mobileLoadCartService(payload);
      
      if (result && result.flag === 1) {
        return res.status(200).send({
          success: true,
          msg: result.msg || "Cart loaded successfully",
          itemsNotInStock: result.items_not_in_stock || []
        });
      } else if (result && result.flag === 0 && result.items_not_in_stock) {
        return res.status(409).send({
          success: false,
          msg: result.msg || "Some items are not in stock",
          itemsNotInStock: result.items_not_in_stock.map(item => ({
            item_id: item.item_id,
            available_quantity: item.available_quantity
          }))
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Cart loading failed',
          itemsNotInStock: []
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        itemsNotInStock: []
      });
    }
  }

  static async getOffers(req, res) {
    try {
      const payload = req.body;
      const { page = 1, limit = 10 } = payload;
      
      const offersPayload = { ...payload, page: parseInt(page), limit: parseInt(limit) };
      const result = await mobileGetOffersService(offersPayload);
      
      if (result && result[0]) {
        const offers = result[0].map(offer => ({
          _id: offer.id.toString(),
          code: offer.coupon_code,
          description: offer.description,
          discount: offer.percent_discount || offer.amount_discount,
          minOrderValue: offer.min_amount
        }));
        
        return res.status(200).send({
          msg: offers.length > 0 ? 'Offers found' : 'No offers found',
          data: {
            docs: offers
          }
        });
      } else {
        return res.status(200).send({
          msg: 'No offers found',
          data: {
            docs: []
          }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {
          docs: []
        }
      });
    }
  }

  static async createOrder(req, res) {
    try {
      const payload = req.body;
      const { items, address, payment_method, store } = payload;
      
      if (!items || !Array.isArray(items) || items.length === 0) {
        return requiredParams(res, 'items array is required!');
      }
      if (!address) return requiredParams(res, 'address is required!');
      if (!payment_method) return requiredParams(res, 'payment_method is required!');
      if (!store) return requiredParams(res, 'store is required!');
      
      const result = await mobileCreateOrderService(payload);
      
      if (result && result.flag === 1) {
        return res.status(200).send({
          data: {
            _id: result.order_id.toString()
          },
          msg: result.msg || "Order created successfully"
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Order creation failed',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }

  static async getOrders(req, res) {
    try {
      const payload = req.body;
      const { page = 1, limit = 10 } = payload;
      
      const ordersPayload = { ...payload, page: parseInt(page), limit: parseInt(limit) };
      const result = await mobileGetOrdersService(ordersPayload);
      
      if (result && result[0]) {
        const orders = result[0].map(order => ({
          _id: order.id.toString(),
          salesOrderNumber: order.order_number || `ORD-${order.id}`,
          orderDate: order.created_at,
          status: order.status,
          total: order.grand_total,
          items: []
        }));
        
        return res.status(200).send({
          data: {
            docs: orders
          }
        });
      } else {
        return res.status(200).send({
          data: {
            docs: []
          }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {
          docs: []
        }
      });
    }
  }

  static async getSingleOrder(req, res) {
    try {
      const payload = req.body;
      const { _id } = payload;
      
      if (!_id) return requiredParams(res, '_id is required!');
      
      const result = await mobileGetSingleOrderService(payload);
      
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Order details",
          data: {
            _id: result.id.toString(),
            salesOrderNumber: result.order_number || `ORD-${result.id}`,
            status: result.status,
            items: result.items || [],
            address: result.address || {},
            total: result.grand_total
          }
        });
      } else {
        return res.status(404).send({
          success: false,
          msg: 'Order not found',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }

  static async getOngoingOrders(req, res) {
    try {
      const payload = req.body;
      
      const result = await mobileGetOngoingOrdersService(payload);
      
      if (result && result[0]) {
        const ongoingOrders = result[0].map(order => ({
          _id: order.id.toString(),
          status: order.status,
          items: order.items || [],
          total: order.grand_total
        }));
        
        return res.status(200).send({
          success: true,
          msg: ongoingOrders.length > 0 ? 'Ongoing orders found' : 'No ongoing orders',
          data: ongoingOrders
        });
      } else {
        return res.status(200).send({
          success: true,
          msg: 'No ongoing orders',
          data: []
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: []
      });
    }
  }

  static async cancelOrder(req, res) {
    try {
      const payload = req.body;
      const { _id } = payload;
      
      if (!_id) return requiredParams(res, '_id is required!');
      
      const result = await mobileCancelOrderService(payload);
      
      if (result && result.flag === 1) {
        return res.status(200).send({
          msg: result.msg || "Order cancelled successfully",
          success: true
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Order cancellation failed'
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong'
      });
    }
  }
}

module.exports = MobileOrderController;

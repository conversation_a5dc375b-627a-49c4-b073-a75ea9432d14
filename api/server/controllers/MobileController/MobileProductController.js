const { mobileGetProductByIdService, mobileGetItemsListService, mobileGetCategoriesService, mobileGetBannersService, mobileGetWishlistService, mobileAddToWishlistService, mobileRemoveFromWishlistService } = require('../../services/MobileServices/MobileProductService');
const { requiredParams } = require('../../utility/requiredCheck');

class MobileProductController {
  
  static async getProductById(req, res) {
    try {
      const { _id } = req.body;

      if (!_id) return requiredParams(res, '_id is required!');

      const payload = { _id };
      const result = await mobileGetProductByIdService(payload);

      if (result) {
        return res.status(200).send({
          data: {
            _id: result.id.toString(),
            name: result.name,
            description: result.description,
            price: result.price,
            images: result.images ? result.images.split(',') : [],
            category: result.category_name,
            stock: result.stock
          }
        });
      } else {
        return res.status(404).send({
          success: false,
          msg: 'Product not found',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }

  static async getItemsList(req, res) {
    try {
      const { page = 1, limit = 10, store, search, categories } = req.body;

      if (!store) return requiredParams(res, 'store is required!');

      const itemsPayload = {
        page: parseInt(page),
        limit: parseInt(limit),
        store_id: store,
        search: search,
        categories: categories ? [categories] : null
      };

      const result = await mobileGetItemsListService(itemsPayload);

      if (result && result[0]) {
        const products = result[0].map(product => ({
          _id: product.id.toString(),
          name: product.name,
          price: product.price,
          images: product.images ? product.images.split(',') : []
        }));

        const totalDocs = result[1] ? result[1][0]?.total || 0 : 0;

        return res.status(200).send({
          success: true,
          msg: products.length > 0 ? 'Products found' : 'No products found',
          data: {
            docs: products,
            totalDocs: totalDocs,
            limit: parseInt(limit),
            page: parseInt(page)
          }
        });
      } else {
        return res.status(200).send({
          success: true,
          msg: 'No products found',
          data: {
            docs: [],
            totalDocs: 0,
            limit: parseInt(limit),
            page: parseInt(page)
          }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {
          docs: [],
          totalDocs: 0,
          limit: 10,
          page: 1
        }
      });
    }
  }

  static async getCategories(req, res) {
    try {
      const payload = req.body;
      const { store } = payload;
      
      if (!store) return requiredParams(res, 'store is required!');
      
      const result = await mobileGetCategoriesService(payload);
      
      if (result && result[0]) {
        const categories = result[0].map(category => ({
          _id: category.id.toString(),
          name: category.name,
          image: category.image,
          description: category.description
        }));
        
        return res.status(200).send({
          success: true,
          msg: categories.length > 0 ? 'Categories found' : 'No categories found',
          data: {
            docs: categories
          }
        });
      } else {
        return res.status(200).send({
          success: true,
          msg: 'No categories found',
          data: {
            docs: []
          }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {
          docs: []
        }
      });
    }
  }

  static async getBanners(req, res) {
    try {
      const payload = req.body;
      
      const result = await mobileGetBannersService(payload);
      
      if (result && result[0]) {
        const banners = result[0].map(banner => ({
          _id: banner.id.toString(),
          title: banner.name,
          image: banner.image,
          link: banner.url
        }));
        
        return res.status(200).send({
          success: true,
          msg: banners.length > 0 ? 'Banners found' : 'No banners found',
          data: {
            docs: banners
          }
        });
      } else {
        return res.status(200).send({
          success: true,
          msg: 'No banners found',
          data: {
            docs: []
          }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {
          docs: []
        }
      });
    }
  }

  static async getWishlist(req, res) {
    try {
      const payload = req.body;
      const { store, page = 1, limit = 10 } = payload;
      
      if (!store) return requiredParams(res, 'store is required!');
      
      const wishlistPayload = { ...payload, page: parseInt(page), limit: parseInt(limit) };
      const result = await mobileGetWishlistService(wishlistPayload);
      
      if (result && result[0]) {
        const wishlistItems = result[0].map(item => ({
          _id: item.product_id.toString(),
          name: item.name,
          price: item.price,
          images: item.images ? item.images.split(',') : []
        }));
        
        return res.status(200).send({
          data: {
            docs: wishlistItems
          }
        });
      } else {
        return res.status(200).send({
          data: {
            docs: []
          }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {
          docs: []
        }
      });
    }
  }

  static async addToWishlist(req, res) {
    try {
      const payload = req.body;
      const { _id } = payload;
      
      if (!_id) return requiredParams(res, '_id is required!');
      
      const result = await mobileAddToWishlistService(payload);
      
      if (result && result.flag === 1) {
        return res.status(200).send({
          msg: result.msg || "Item added to wishlist"
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Failed to add to wishlist'
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong'
      });
    }
  }

  static async removeFromWishlist(req, res) {
    try {
      const payload = req.body;
      const { _id } = payload;
      
      if (!_id) return requiredParams(res, '_id is required!');
      
      const result = await mobileRemoveFromWishlistService(payload);
      
      if (result && result.flag === 1) {
        return res.status(200).send({
          msg: result.msg || "Item removed from wishlist"
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Failed to remove from wishlist'
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong'
      });
    }
  }
}

module.exports = MobileProductController;

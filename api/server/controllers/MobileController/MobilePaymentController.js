const { mobileInitiatePaymentService, mobileCapturePaymentService, mobileGetPaymentStatusService } = require('../../services/MobileServices/MobilePaymentService');
const { requiredParams } = require('../../utility/requiredCheck');

class MobilePaymentController {
  
  static async initiatePayment(req, res) {
    try {
      const payload = req.body;
      const { order_id, amount, currency = 'INR' } = payload;
      
      if (!order_id) return requiredParams(res, 'order_id is required!');
      if (!amount) return requiredParams(res, 'amount is required!');
      
      const result = await mobileInitiatePaymentService(payload);
      
      if (result && result.flag === 1) {
        return res.status(200).send({
          success: true,
          msg: result.msg || "Payment initiated successfully",
          data: {
            payment_id: result.payment_id,
            order_id: result.order_id,
            amount: result.amount,
            currency: result.currency,
            key: result.razorpay_key
          }
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Payment initiation failed',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }

  static async capturePayment(req, res) {
    try {
      const payload = req.body;
      const { payment_id, order_id, signature } = payload;
      
      if (!payment_id) return requiredParams(res, 'payment_id is required!');
      if (!order_id) return requiredParams(res, 'order_id is required!');
      if (!signature) return requiredParams(res, 'signature is required!');
      
      const result = await mobileCapturePaymentService(payload);
      
      if (result && result.flag === 1) {
        return res.status(200).send({
          success: true,
          msg: result.msg || "Payment captured successfully",
          data: {
            payment_status: result.payment_status,
            order_status: result.order_status
          }
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Payment capture failed',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }

  static async getPaymentStatus(req, res) {
    try {
      const payload = req.body;
      const { payment_id } = payload;
      
      if (!payment_id) return requiredParams(res, 'payment_id is required!');
      
      const result = await mobileGetPaymentStatusService(payload);
      
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Payment status retrieved",
          data: {
            payment_id: result.payment_id,
            status: result.status,
            amount: result.amount,
            currency: result.currency,
            order_id: result.order_id
          }
        });
      } else {
        return res.status(404).send({
          success: false,
          msg: 'Payment not found',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }
}

module.exports = MobilePaymentController;

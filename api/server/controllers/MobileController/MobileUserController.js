const { mobileSendOtpService, mobileVerifyOtpService, mobileRegisterUserService, mobileUpdateProfileService, mobileCreateAddressService, mobileGetAddressesService, mobileDeleteAddressService, mobileGetNearbyStoreService } = require('../../services/MobileServices/MobileUserService');
const { msg91OtpService, msg91WhatsappService } = require('../../services/NotificationServices/MSG91Service');
const { requiredParams } = require('../../utility/requiredCheck');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const db = require('../../../config/connection');

class MobileUserController {
  
  static async sendOtp(req, res) {
    const payload = req.body;
    const { mobile } = payload;
    const otp = Math.floor(1000 + Math.random() * 9000).toString();
    console.log("Generated OTP:", otp, "for mobile:", mobile);
    const encryptedOtp = await bcrypt.hash(otp, 10);
    const userPayload = { ...payload, otp: encryptedOtp, user_type: "Customer" };

    if (mobile == null || mobile == undefined)
      return requiredParams(res, "mobile is required!", 406);

    if (!/^\d{10}$/.test(mobile)) {
      return res.status(400).send({
        success: false,
        msg: "Mobile number must be 10 digits long",
        data: {},
      });
    }

    try {
      const result = await mobileSendOtpService(userPayload);

      if (result && result.flag === 1) {
        // Only send SMS/WhatsApp in production environment
        if (process.env.NODE_ENV && process.env.NODE_ENV.toLowerCase().trim() === 'production') {
          // Check if user is verified to determine template
          const isverified = `select otp_verified from temp_user where mobile = ?`;
          const [[verifiedcheck]] = await db.promise().query(isverified, mobile);
          console.log("verified Check:>>", verifiedcheck);

          try {
            // Send SMS OTP using MSG91
            if (verifiedcheck && verifiedcheck.otp_verified === 1) {
              // Login template for verified users
              await msg91OtpService(mobile, otp, "68932763bafa0d3d34206a36");
            } else {
              // Signup template for new users
              await msg91OtpService(mobile, otp, "68932763bafa0d3d34206a36");
            }

            // Send WhatsApp OTP using MSG91
            // await msg91WhatsappService(mobile, "login_otp", { OTP: otp });
          } catch (smsError) {
            console.log("SMS/WhatsApp sending error:", smsError);
            // Don't fail the API if SMS fails, just log the error
          }
        } else {
          console.log("SMS/WhatsApp skipped in staging. OTP:", otp);
        }

        return res.status(200).send({
          success: true,
          msg: "Otp sent successfully.",
          data: {},
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || "Failed to send OTP",
          data: {},
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async verifyOtp(req, res) {
    const payload = req.body;
    const { otp, mobile, fcm_token } = payload;

    console.log("Verifying OTP:", otp, "for mobile:", mobile);

    // Add fcm_token to payload for stored procedure if provided
    const verifyPayload = { ...payload };
    if (fcm_token) {
      verifyPayload.fcm_token = fcm_token;
    }

    if (mobile == null || mobile == undefined)
      return requiredParams(res, "mobile is required!", 406);
    if (otp == null || otp == undefined)
      return requiredParams(res, "otp is required!", 406);

    if (!/^\d{10}$/.test(mobile)) {
      return res.status(400).send({
        success: false,
        msg: "Mobile number must be 10 digits long",
        data: {},
      });
    }

    if (!/^\d{4}$/.test(otp)) {
      return res.status(400).send({
        success: false,
        msg: "OTP must be 4 digits only",
        data: {},
      });
    }

    // Check if user is active
    const isactive = `select is_active from users where mobile = ?`;
    const [[activecheck]] = await db.promise().query(isactive, mobile);
    if (activecheck && activecheck.is_active === 0) {
      return res.status(403).send({
        success: false,
        msg: "Your account is disabled",
        data: {},
      });
    }

    // Check OTP from temp_user table with database time comparison
    const checkSQL = `select count(*) as status, id as temp_id, sent_otp as otp, user_id, otp_verified, mobile, updated_at,
                      DATE_ADD(updated_at,INTERVAL 10 MINUTE) as expire_time,
                      NOW() as current_db_time,
                      CASE WHEN NOW() > DATE_ADD(updated_at,INTERVAL 10 MINUTE) THEN 1 ELSE 0 END as is_expired
                      from temp_user where mobile = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, mobile);

    try {
      // Allow 0000 OTP only in staging environment
      const isTestOtp = otp === "0000" && process.env.NODE_ENV && process.env.NODE_ENV.toLowerCase().trim() !== 'production';

      // Check expiration using database time comparison
      if (!isTestOtp && isExist.is_expired === 1) {
        console.log("OTP expired! DB Time:", isExist.current_db_time, "Expire Time:", isExist.expire_time);
        return requiredParams(res, "otp expired!");
      }

      if (isTestOtp) {
        console.log("Using test OTP 0000 - bypassing expiration check");
      }

      const comparison = await bcrypt.compare(otp, isExist.otp);

      if (isExist.otp_verified === 1 && (comparison || isTestOtp)) {
        // Existing verified user - login
        const userData = await getUserDetailService({ user_id: isExist.user_id });

        const token = jwt.sign({ result: mobile }, process.env.JWT_KEY, {
          expiresIn: "30d",
        });

        const userDatadetails = {
          ...userData,
          token: token,
        };

        return res.status(200).send({
          success: true,
          msg: "Login Successfully",
          data: userDatadetails,
        });
      } else if (comparison == true || isTestOtp) {
        // New user - verify OTP and create user
        const user = await mobileVerifyOtpService(verifyPayload);

        const result = await getUserDetailService({
          user_id: user.user_id,
          type: "newuser",
        });

        const data = { ...result, is_newuser: user.is_newuser };

        return res.status(200).send({
          success: true,
          msg: "User verified successfully.",
          data: data,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "otp incorrect.",
          data: {},
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async registerUser(req, res) {
    const payload = req.body;
    console.log("Payload::>>", payload);

    if (!payload.hasOwnProperty("user_id") || payload.user_id === null || payload.user_id === undefined) {
      return requiredParams(res, "user_id is required!", 406);
    }

    if (!payload.hasOwnProperty("first_name") || payload.first_name.trim() === "") {
      return requiredParams(res, "first_name is required!", 406);
    }

    if (!payload.hasOwnProperty("last_name") || payload.last_name.trim() === "") {
      return requiredParams(res, "last_name is required!", 406);
    }

    if (!payload.hasOwnProperty("email") || payload.email.trim() === "") {
      return requiredParams(res, "email is required!", 406);
    }

    try {
      const checkSQL = `SELECT count(*) as status from users where id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [payload.user_id]);
      console.log("isExist::>>", isExist);

      if (isExist.status > 0) {
        const data = await mobileRegisterUserService(payload);
        console.log("Data:>>", data);

        const userData = await getUserDetailService({ user_id: payload.user_id });
        console.log("Userdata::>>", userData);

        const token = jwt.sign({ result: userData.mobile }, process.env.JWT_KEY, {
          expiresIn: "30d",
        });

        const userDatadetails = { ...userData, token: token };

        return res.status(200).send({
          success: true,
          msg: "User registration is successfull",
          data: userDatadetails,
        });
      } else {
        return res.status(500).send({
          success: false,
          msg: "User does not exists",
          data: {},
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }

  static async updateProfile(req, res) {
    try {
      const payload = req.body;
      const { name, email } = payload;
      
      if (!name) return requiredParams(res, 'name is required!');
      if (!email) return requiredParams(res, 'email is required!');
      
      const result = await mobileUpdateProfileService(payload);
      
      if (result && result.flag === 1) {
        return res.status(200).send({
          msg: result.msg || "Profile updated successfully"
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Profile update failed'
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong'
      });
    }
  }

  static async getNearbyStore(req, res) {
    try {
      const { coordinates } = req.body;

      if (!coordinates || !coordinates[0] || !coordinates[1]) {
        return requiredParams(res, 'coordinates are required!');
      }

      const storePayload = {
        longitude: parseFloat(coordinates[0]),
        latitude: parseFloat(coordinates[1])
      };

      const result = await mobileGetNearbyStoreService(storePayload);

      if (result && result.id) {
        return res.status(200).send({
          success: true,
          data: {
            nearbyStore: {
              id: result.id.toString(),
              name: result.name,
              address: result.description,
              coordinates: [parseFloat(result.lng), parseFloat(result.lat)]
            }
          }
        });
      } else {
        return res.status(404).send({
          success: false,
          msg: 'No nearby store found',
          data: {}
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: {}
      });
    }
  }

  static async createOrUpdateAddress(req, res) {
    try {
      const payload = req.body;
      const { name, phone, type, line1, line2, landmark, state, city, pincode, coordinates } = payload;

      if (!name) return requiredParams(res, 'name is required!');
      if (!phone) return requiredParams(res, 'phone is required!');
      if (!type) return requiredParams(res, 'type is required!');
      if (!line1) return requiredParams(res, 'line1 is required!');
      if (!city) return requiredParams(res, 'city is required!');
      if (!pincode) return requiredParams(res, 'pincode is required!');
      if (!coordinates || !coordinates[0] || !coordinates[1]) {
        return requiredParams(res, 'coordinates are required!');
      }

      const addressPayload = {
        ...payload,
        longitude: coordinates[0],
        latitude: coordinates[1]
      };

      const result = await mobileCreateAddressService(addressPayload);

      if (result && result.flag === 1) {
        return res.status(200).send({
          success: true,
          msg: result.msg || "Address saved successfully"
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Address save failed'
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong'
      });
    }
  }

  static async deleteAddress(req, res) {
    try {
      const payload = req.body;
      const { _id } = payload;

      if (!_id) return requiredParams(res, '_id is required!');

      const result = await mobileDeleteAddressService(payload);

      if (result && result.flag === 1) {
        return res.status(200).send({
          msg: result.msg || "Address deleted successfully"
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: result?.msg || 'Address deletion failed'
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong'
      });
    }
  }

  static async getAddresses(req, res) {
    try {
      const { page = 1, limit = 10 ,user_id} = req.body;

      const addressPayload = { user_id, page: parseInt(page), limit: parseInt(limit) };
      // console.log("Address payload:", addressPayload);
      const result = await mobileGetAddressesService(addressPayload);
      // console.log("Address result:", result);

      if (result && result[0]) {
        const addresses = result[0].map(address => ({
          _id: address.id.toString(),
          name: address.name,
          phone: address.phone,
          type: address.address_type,
          line1: address.address1,
          line2: address.address2,
          city: address.city,
          pincode: address.pincode,
          coordinates: [address.longitude, address.latitude]
        }));

        return res.status(200).send({
          success: true,
          data: addresses
        });
      } else {
        return res.status(200).send({
          success: true,
          data: []
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || 'Something went wrong',
        data: []
      });
    }
  }
}

// Helper function to get user details
const getUserDetailService = async (payload) => {
  try {
    console.log("Payload in getUserDetailService::>>", payload);
    const procedure = `CALL spMobileGetUserDetails(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Raw results from getUserDetails procedure:", results);

    // The stored procedure returns results in the first array
    if (results && results[0] && results[0].length > 0) {
      return results[0][0]; // Get the first row from the first result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in getUserDetailService", error);
    throw error;
  }
};

module.exports = MobileUserController;

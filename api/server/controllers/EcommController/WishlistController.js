const { updateWishlistService, getWishlistService } = require("../../services/EcommServices/WishlistService");
const { requiredParams } = require('../../utility/requiredCheck');

class WishlistController {
  static async updateWishlist(req, res) {
    const payload = req.body;
    const { product_id, user_id } = payload;
    if (product_id == null || product_id == undefined) return requiredParams(res, 'product_id is required!');
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!');
    //console.log('create wishlist payload', payload);
    const [result] = await updateWishlistService(payload);
    //console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }
  
  static async getWishlist(req, res) {
    const payload = req.body;
    const { user_id } = payload;
    if (user_id == null || user_id == undefined) return requiredParams(res, 'user_id is required!');
    const [result, meta] = await getWishlistService(payload);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: (result.length !== 0) ? 'Fav Product List' : 'No records',
          data: result,
          meta: meta[0] || { total: 0 }
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: []
      });
    }
  }
}

module.exports = WishlistController;
const { requiredParams } = require("../../utility/requiredCheck");
const db = require("../../../config/connection");
const fs = require("fs");
const { BlobServiceClient } = require("@azure/storage-blob");
const { createReadStream, unlinkSync } = require("fs");
const { v4: uuidv4 } = require("uuid");
const {
  createSubCategoryGroupService,
  updateSubCategoryGroupService,
  getSubCategoryGroupService,
} = require("../../services/EcommServices/SubCategoryGroupService");
const {
  uploadImageService,
  deleteImageService,
} = require("../../services/EcommServices/ProductService");

// Initialize Azure Storage client
const blobServiceClient = BlobServiceClient.fromConnectionString(
  process.env.AZURE_STORAGE_CONNECTION_STRING
);

class SubCategoryGroupController {
    
  static async createSubCategoryGroup(req, res) {
    const payload = req.body;
    const { sub_category_id, name } = payload;

    //console.log('createCategory payload', payload);
    if (sub_category_id == null || sub_category_id == undefined)
      return requiredParams(res, "sub_category_id is required!");

    if (name == null || name == undefined)
      return requiredParams(res, "name is required!");

    const [result] = await createSubCategoryGroupService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {
            sub_category_group_id: result.sub_category_group_id,
          },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async updateSubCategoryGroup(req, res) {
    const payload = req.body;
    const { sub_category_group_id, name, description } = payload;

    if (sub_category_group_id == null || sub_category_group_id == undefined)
      return requiredParams(res, "sub_category_group_id is required!");

    if (name == null || name == undefined)
      return requiredParams(res, "name is required!");

    if (description === undefined)
      return requiredParams(res, "description is required!");

    //console.log('updateSubCategoryGroup payload', payload);
    const [result] = await updateSubCategoryGroupService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: [],
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getSubCategoryGroup(req, res) {
    const payload = req.body;

    //console.log('req.body payload', req.body);
    const { sub_category_id, category_id } = payload;
    //console.log('getSubCategoryGroup payload', payload);

    if (sub_category_id == null || sub_category_id == undefined)
      return requiredParams(res, "sub_category_id is required!");

    const [result, meta] = await getSubCategoryGroupService(payload);
    //console.log('result', result);

    const All = {
      sub_category_group_id: 0,
      sub_category_id: sub_category_id,
      sub_category_group_name: "All",
      description: null,
      category_id: category_id,
      sub_category_name: "",
      category_name: "",
      sub_category_group_image: "",
      image_id: null,
      created_at: "2021-10-01T17:44:06.000Z",
      updated_at: "2021-10-01T17:44:06.000Z",
    };

    let group = [];
    group.push(All);
    result.map((m, i) => {
      group.push(m);
    });

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg:
            result.length !== 0
              ? "SubCategoryGroup List"
              : "No SubCategoryGroup",
          data: group,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async UploadSubCategoryGroupImage(req, res) {
    const { sub_category_group_id } = req.body;

    try {
      // Get file extension
      const fileExtension = req.file.originalname
        .split(".")
        .pop()
        .toLowerCase();
      const filename = uuidv4() + "." + fileExtension;

      // Determine container based on environment
      const containerName =
        process.env.NODE_ENV === "PRODUCTION"
          ? process.env.AZURE_PROD_CONTAINER
          : process.env.AZURE_STAG_CONTAINER;

      const containerClient =
        blobServiceClient.getContainerClient(containerName);
      const blobName = `SubcategoryGroup/SubcategoryGroup-${sub_category_group_id}/${filename}`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      // Upload to Azure Blob Storage
      await blockBlobClient.uploadStream(
        fs.createReadStream(req.file.path),
        undefined,
        undefined,
        {
          blobHTTPHeaders: {
            blobContentType: `image/${
              fileExtension === "jpg" ? "jpeg" : fileExtension
            }`,
          },
        }
      );

      // Clean up temp file
      fs.unlinkSync(req.file.path);

      // Construct the public URL
      const locationUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT}.blob.core.windows.net/${containerName}/${blobName}`;

      const request = { sub_category_group_id, location: locationUrl };

      try {
        const [result] = await uploadImageService(request);

        if (result) {
          return res.status(200).send({
            success: true,
            msg: `${result.msg}`,
            data: {
              image_id: result.image_id,
              url: result.url,
            },
          });
        }
      } catch (error) {
        return res.status(200).send({
          success: false,
          msg: error.message || `Something went wrong`,
          data: [],
        });
      }
    } catch (error) {
      // Clean up temp file on error
      if (req.file) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (unlinkError) {
          console.log("Error cleaning up temp file:", unlinkError);
        }
      }

      console.log(
        "Error occurred while trying to upload to Azure Blob Storage",
        error
      );
      return res.status(200).send({
        success: false,
        msg: `Error occurred while trying to upload to Azure Blob Storage`,
        data: error,
      });
    }
  }

  static async deleteSubCategoryGroupImage(req, res) {
    try {
      const { sub_category_group_image_id } = req.body;

      if (!sub_category_group_image_id) {
        return res.status(400).send({
          success: false,
          msg: "sub_category_group_image_id is required",
          data: [],
        });
      }

      // Get image URL from database
      const [[imageRecord]] = await db
        .promise()
        .query(
          `SELECT images as url FROM sub_category_group_images WHERE id = ?`,
          [sub_category_group_image_id]
        );

      if (!imageRecord?.url) {
        return res.status(200).send({
          success: false,
          msg: "No image found in database",
          data: {},
        });
      }

      // Delete from Azure Blob Storage
      const containerName =
        process.env.NODE_ENV === "PRODUCTION"
          ? process.env.AZURE_PROD_CONTAINER
          : process.env.AZURE_STAG_CONTAINER;

      const containerClient =
        blobServiceClient.getContainerClient(containerName);

      const urlParts = new URL(imageRecord.url);
      let blobName = urlParts.pathname.substring(1);

      if (blobName.startsWith(containerName + "/")) {
        blobName = blobName.substring(containerName.length + 1);
      }

      await containerClient.deleteBlob(blobName);

      // Delete from database
      await deleteImageService({ sub_category_group_image_id });

      return res.status(200).send({
        success: true,
        msg: "Image deleted successfully",
        data: imageRecord.url,
      });
    } catch (error) {
      console.log("Error deleting image:", error);
      return res.status(200).send({
        success: false,
        msg: "Failed to delete image: " + error.message,
        data: [],
      });
    }
  }

}

module.exports = SubCategoryGroupController;

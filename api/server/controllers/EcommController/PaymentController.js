const { requiredParams } = require("../../utility/requiredCheck");
const db = require("../../../config/connection");
const Razorpay = require("razorpay");
const {
  placeDeliveryOrderService,
} = require("../../services/EcommServices/DeliveryService");
const {
  getOrderByIDService,
  updateGofrugalOrderService,
} = require("../../services/EcommServices/OrderService");
const {
  initiatePaymentService,
  capturedPaymentService,
  PaymentListService,
  initiateInvoiceService,
  getPaymentKeys,
  PaymentByIDService,
  refundPaymentService,
} = require("../../services/EcommServices/PaymentService");
const {
  getnotificationService,
  CallWebhook,
} = require("../../services/NotificationServices/NotificationServices");
const { result } = require("lodash");
const { json } = require("body-parser");
const {
  getgofrugalkeys,
} = require("../../services/EcommServices/GofrugalService");

class PaymentController {
  static async initiatePayment(req, res) {
    const [[payment]] = await getPaymentKeys();
    //console.log('result', payment);
    // const razorpay = new Razorpay({
    //   key_id: process.env.RAZOR_KEY,
    //   key_secret: process.env.RAZOR_SECRET,
    // });
    // return false;

    const razorpay = await new Razorpay({
      key_id: payment.key,
      key_secret: payment.secret,
    });

    const payload = req.body;
    const { order_id, grand_total, user_id } = payload;

    if (user_id == null || user_id == undefined)
      return requiredParams(res, "User id is required!");

    if (order_id == null || order_id == undefined)
      return requiredParams(res, "Order id is required!");

    if (grand_total == null || grand_total == undefined)
      return requiredParams(res, "Grand total amount is required!");

    try {
      const newTotal = grand_total * 100;
      const [[order_check]] = await db.promise()
        .query(`select o.id,o.payment_id,p.reference_id,u.email ,u.mobile from orders o
                left join payment p on p.id = o.payment_id
                left join users u on u.id = o.user_id
                where o.id  = ${order_id} and o.payment_id IS NOT NULL`);

      console.log("order_check", order_check);

      if (order_check != null || order_check != undefined) {
        return res.status(200).send({
          success: true,
          msg: "order already initiated!",
          data: {
            entity: "order",
            razorpay_id: order_check.reference_id,
            razorpay_key_id: payment.key,
            email: order_check.email,
            mobile: order_check.mobile,
            order_id: order_id,
          },
        });
      } else {
        let todayDateTime = new Date();
        todayDateTime = Math.floor(todayDateTime.getTime() / 1000.0);

        let expTime = new Date();
        expTime.setHours(expTime.getHours() + 24);
        expTime = Math.floor(expTime.getTime() / 1000.0);

        const invPayload = {
          amount: newTotal,
          receipt: `Receipt No. ${order_id}`,
          currency: `INR`,
        };

        razorpay.orders.create(invPayload, async function (err, order) {
          if (err) {
            console.log(err);
            return res.status(200).send({
              success: false,
              msg: err.error.description || `Something went wrong`,
              data: {},
            });
          } else {
            const spPayload = {
              ...order,
              ...{
                order_id: order.id,
                junq_order_id: order_id,
                junq_user_id: user_id,
                grand_total,
              },
            };

            try {
              const [[isExist]] = await db
                .promise()
                .query(
                  `select u.email ,u.mobile from users u where u.id  = ${user_id}`
                );
              console.log("isExist", isExist);

              const [result] = await initiatePaymentService(spPayload);
              console.log("initiatePaymentService result", result);

              if (Boolean(result.status) == true) {
                return res.status(200).send({
                  success: Boolean(result.status),
                  msg: result.msg,
                  data: {
                    ...order,
                    ...{
                      razorpay_id: order.id,
                      razorpay_key_id: payment.key,
                      email: isExist.email,
                      mobile: isExist.mobile,
                      order_id: order_id,
                    },
                  },
                });
              } else {
                return res.status(200).send({
                  success: Boolean(result.status),
                  msg: result.msg,
                  data: {},
                });
              }
            } catch (error) {
              return res.status(200).send({
                success: false,
                msg: error.message || `Something went wrong`,
                data: {},
              });
            }
          }

          // initiateInvoiceService(payload).then(([[result]]) => {
          //   console.log('initiateInvoiceService response', result);

          //   const invPayload = {
          //     type: "invoice",
          //     description: `Junq invoice - ${result.full_name || ''}`,
          //     // partial_payment: false,
          //     customer: {
          //       name: result.full_name || '',
          //       contact: result.phone || '',
          //       email: result.email || '',
          //       billing_address: {
          //         line1: `${result.address1 || ''} ${result.address2 || ''}`,
          //         line2: `${result.address1 || ''} ${result.address2 || ''}`,
          //         zipcode: result.pincode || '',
          //         city: result.city || '',
          //         state: result.state || '',
          //         country: "India"
          //       },
          //       shipping_address: {
          //         line1: `${result.address1 || ''} ${result.address2 || ''}`,
          //         line2: `${result.address1 || ''} ${result.address2 || ''}`,
          //         zipcode: result.pincode || '',
          //         city: result.city || '',
          //         state: result.state || '',
          //         country: "India"
          //       }
          //     },
          //     line_items: result.line_items,
          //     sms_notify: 1,
          //     email_notify: 1,
          //     draft: 1,
          //     date: todayDateTime,
          //     expire_by: expTime,
          //     receipt: `Receipt No. ${result.order_id}`,
          //     comment: "",
          //     terms: "",
          //     notes: {
          //       notes_key_1: "",
          //       notes_key_2: ""
          //     }
          //   };
          //   // console.log('invPayload:', invPayload)
          //   razorpay.invoices.create(invPayload, async function (err, inv) {
          //     if (err) {
          //       console.log(err);
          //       return res.status(200).send({
          //         success: false,
          //         msg: err.error.description || `Something went wrong`,
          //         data: {}
          //       });
          //     } else {
          //       console.log('invoice', inv);
          //       razorpay.invoices.issue(inv.id, async function (err2, done) {
          //         if (err2) {
          //           console.log('invoices.issue', err2);
          //         } else {
          //           console.log('invoices.issue', done);
          //           const spPayload = { ...done, ...{ junq_order_id: order_id, junq_user_id: user_id, grand_total } };

          //           if (done) {
          //             try {
          //               const [result] = await initiatePaymentService(spPayload);
          //               console.log('initiatePaymentService result', result);
          //               if (Boolean(result.status) == true) {
          //                 return res.status(200).send({
          //                   success: Boolean(result.status),
          //                   msg: result.msg,
          //                   data: { ...done, ...{ razorpay_id: done.order_id, short_url: done.short_url, razorpay_key_id: payment.key, } },
          //                 });
          //               } else {
          //                 return res.status(200).send({
          //                   success: Boolean(result.status),
          //                   msg: result.msg,
          //                   data: {},
          //                 });
          //               }
          //             } catch (error) {
          //               return res.status(200).send({
          //                 success: false,
          //                 msg: error.message || `Something went wrong`,
          //                 data: {}
          //               });
          //             }
          //           }
          //         }
          //       });
          //     }
          //   });
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async capturedPayment(req, res) {
    const body = req.body;
    console.log("log", body);

    const { payload } = body;
    const { payment, refund } = payload;
    const { event } = body;
    let entity;

    if (
      event == "refund.created" ||
      event == "refund.processed" ||
      event == "refund.failed" ||
      event == "refund.speed_changed"
    ) {
      entity = refund.entity;
    } else if (
      event == "paymnent.authorized" ||
      event == "payment.captured" ||
      event == "payment.failed"
    ) {
      entity = payment.entity;
    }

    //console.log('payload::', payload);
    console.log("payment::", payment);

    const sPayload = { event, ...entity };
    console.log(sPayload);

    // const { order_id, user_id, status } = body;
    // if (order_id == null || order_id == undefined) return requiredParams(res, 'Order id is required!');
    // if (user_id == null || user_id == undefined) return requiredParams(res, 'user id is required!');
    // if (status == null || status == undefined) return requiredParams(res, 'payment status is required!');

    try {
      const [result] = await capturedPaymentService(sPayload);
      console.log("result", result);

      // const reqPayload = { store_code: entity.notes.store_code || "junqstore001", drop_instruction_text: entity.notes.store_code || "", order_id: entity.notes.order_id };

      // const delOrderState = await placeDeliveryOrderService(reqPayload);
      // console.log('delOrderState', delOrderState);

      if (result) {
        const sql = `select user_id, store_id, status from orders where id = ?`;
        const [[order]] = await db.promise().query(sql, result.v_order_id);

        if (order.status == "order_paid") {
          await getnotificationService({
            receiver_id: order.user_id,
            data: {
              title: "PAYMENT DONE",
              body: "Your payment has been done.",
              type: "order",
              order_id: JSON.stringify(result.v_order_id),
            },
          });
          await updateGofrugalOrderService({ order_id: result.v_order_id });

          const sql = `select events from webhook where brand_id =1`;
          const [[{ events }]] = await db.promise().query(sql);

          if (events.includes("placed")) {
            const webhookpayload = {
              store_id: order.store_id,
              order_status: "placed",
              order_id: result.v_order_id,
            };

            await CallWebhook(webhookpayload);
          }
        }

        return res.status(200).send({
          success: Boolean(result.status),
          msg: result.msg,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async refundPayment(req, res) {
    const payload = req.body;
    const [[payment]] = await getPaymentKeys();
    const { paymentId, amount, reason, refund_type } = payload;

    const razorpay = await new Razorpay({
      key_id: payment.key,
      key_secret: payment.secret,
    });

    if (paymentId == null || paymentId == undefined)
      return requiredParams(res, "Payment id is required!");

    if (amount == null || amount == undefined)
      return requiredParams(res, " amount is required!");

    if (refund_type == null || refund_type == undefined)
      return requiredParams(res, "refund_type  is required!");

    console.log(paymentId);

    const [[{ sumamount }]] = await db
      .promise()
      .query(
        `SELECT  SUM(amount) as sumamount  from refund  WHERE  payment_id= ?`,
        paymentId
      );
    const [[{ totalamount }]] = await db
      .promise()
      .query(
        `SELECT  amount as totalamount  from payment  WHERE  razorpay_ref_id=?`,
        paymentId
      );

    console.log(
      "paymentid",
      paymentId,
      "sumamount",
      sumamount,
      "totalamount",
      totalamount
    );

    if ((sumamount == null ? 0 : sumamount + amount) > totalamount) {
      return requiredParams(res, "refund amount exceeded");
    } else {
      const options = {
        amount: amount * 100,
        speed: "normal",
      };

      try {
        const presult = await razorpay.payments.refund(paymentId, options);
        const sql = `select user_id from payment where razorpay_ref_id = ?`;
        const [[{ user_id }]] = await db.promise().query(sql, paymentId);

        console.log(presult);
        console.log(user_id);

        if (presult.id) {
          console.log("hello", user_id, paymentId, amount);
          const spPayload = {
            user_id,
            payment_id: paymentId,
            amount,
            reason,
            status: presult.status,
            refund_type,
            speed_type: presult.speed_processed,
            refund_id: presult.id,
          };
          console.log(spPayload);

          const [result] = await refundPaymentService(spPayload);
          console.log("initiatePaymentService result", result);

          if (Boolean(result.status) == true) {
            return res.status(200).send({
              success: Boolean(result.status),
              msg: result.msg,
              data: spPayload,
            });
          } else {
            return res.status(200).send({
              success: Boolean(result.status),
              msg: result.msg,
              data: {},
            });
          }
        } else {
          return res.status(200).send({
            success: false,
            msg: "something error",
            data: presult,
          });
        }
      } catch (error) {
        return res.status(200).send({
          success: false,
          msg: error || "Something went error",
          data: {},
        });
      }
    }
  }

  static async getPaymentList(req, res) {
    const payload = req.body;

    //console.log('getPaymentList payload', payload);
    try {
      const [result, meta] = await PaymentListService(payload);
      //console.log('result', result);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Payment List" : "No Payments",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getPaymentByID(req, res) {
    const payload = req.body;

    //console.log('getPaymentList payload', payload);
    const { payment_id } = payload;

    if (payment_id == null || payment_id == undefined)
      return requiredParams(res, "payment_id is required!");

    const checkSQL = `select count(*) as status from payment where id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, payment_id);
    console.log("isExist", isExist);

    try {
      if (isExist.status != 0) {
        const [result] = await PaymentByIDService(payload);

        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Payment details by ID",
            data: result,
          });
        }
      } else {
        return res.status(200).send({
          success: false,
          msg: "Payment ID does not exist!",
          data: result,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  
}

module.exports = PaymentController;

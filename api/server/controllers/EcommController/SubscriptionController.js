const {
  getSubscriptionService,
} = require("../../services/EcommServices/SubscriptionService");
const { requiredParams } = require("../../utility/requiredCheck");
const aws = require("aws-sdk");
const fs = require("fs");
const db = require("../../../config/connection");

class SubscriptionController {
  static async getSubscriptionList(req, res) {
    const payload = req.body;
    const { user_id, product_id, order_id } = payload;

    if (user_id == null || user_id == undefined)
      return requiredParams(res, "user_id is required!");

    if (product_id == null || product_id == undefined)
      return requiredParams(res, "product_id is required!");

    if (order_id == null || order_id == undefined)
      return requiredParams(res, "order_id is required!");

    const [result] = await getSubscriptionService(payload);
    console.log("result", result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Subscription List" : "No Subscription",
          data: result,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }
}

module.exports = SubscriptionController;

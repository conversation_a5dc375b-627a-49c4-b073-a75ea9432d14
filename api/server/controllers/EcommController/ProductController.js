const { getProductsMayLikes, createProductService, getProductService, getProductByIDService, getProductsByCategoryService, updateProductService, updateVariantService, createVariantService, getVariantsService, validateDiscountService, uploadStoreService, getFiveProductsPerCategoryService, filterProductsService } = require("../../services/EcommServices/ProductService");
const { requiredParams } = require("../../utility/requiredCheck");
const csvtojson = require("csvtojson");
const db = require('../../../config/connection');
const { uploadImageService, deleteImageService, uploadInventoryService } = require('../../services/EcommServices/ProductService');
const { isUndefined } = require("lodash");
const { createReadStream, unlinkSync } = require("fs");
const { v4: uuidv4 } = require('uuid');
const { BlobServiceClient } = require('@azure/storage-blob');

// Create a BlobServiceClient
const blobServiceClient = BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING);
const axios=require("axios");

// aws.config.setPromisesDependency();
// aws.config.update({
//   accessKeyId: process.env.accessKeyId,
//   secretAccessKey: process.env.secretAccessKey,
//   region: process.env.region
// });
// const s3 = new aws.S3();

class ProductController {
  static async productsMayLikes(req, res) {
    const payload = req.body;
    // const { product_id } = payload;
    // if (product_id == null || product_id == undefined) return requiredParams(res, 'product_id is required!');
    const [result, meta] = await getProductsMayLikes(payload);
    console.log("result", result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Product list" : "No products",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getProducts(req, res) {
    const payload = req.body;
    // payload check here
    //console.log('getProducts payload', payload);
    const [result, meta] = await getProductService(payload);
    // console.log('result', result);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Product list" : "No products",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getFiveProductsPerCategory(req, res) {
    const payload = req.body;
    const { store_id } = payload;
    if (store_id == null || store_id == undefined)
      return requiredParams(res, "store_id is required!", 406, []);
    const result = await getFiveProductsPerCategoryService(req.body);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Five Products from each category",
          data: result[0],
        });
      }
    } catch (error) {
      return res.status(400).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getProductByID(req, res) {
    const payload = req.body;
    //console.log('getProducts payload', payload);

    try {
      const [result] = await getProductByIDService(payload);
      console.log("result....", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Product by ID",
          data: result,
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: `Something went wrong with service`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getProductsByCategory(req, res) {
    const payload = req.body;
    //console.log('getProducts payload', payload);
    const [result, meta] = await getProductsByCategoryService(payload);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Product by Category list" : "No products",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async filterProducts(req, res) {
    const payload = req.body;
    let { price, page, size } = req.body;
    if (price) {
      if (!Array.isArray(price)) {
        payload.price = [price, price];
        if (payload.price.length % 2 !== 0)
          return requiredParams(
            res,
            "Price range array contains invalid number of elements.",
            406,
            []
          );
      }
    }
    if (page < 0 || size < 0)
      return requiredParams(
        res,
        "page and size should be positive numbers",
        406,
        []
      );
    const [result, meta] = await filterProductsService(payload);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Filtered products list" : "No products",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async createProduct(req, res) {
    const payload = req.body;
    //console.log('createProduct payload', payload);
    const { name } = payload;
    if (name == null || name == undefined)
      return requiredParams(res, "Name is required!");
    try {
      const [result] = await createProductService(payload);
      //console.log('result', result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async updateProduct(req, res) {
    const payload = req.body;
    //console.log('updateProduct payload', payload);
    const { product_id, name } = payload;
    if (product_id == null || product_id == undefined)
      return requiredParams(res, "product_id is required!");
    if (name == null || name == undefined)
      return requiredParams(res, "Name is required!");
    try {
      const [result] = await updateProductService(payload);
      //console.log('result', result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async createVariant(req, res) {
    const payload = req.body;
    console.log("createVariant payload", payload);
    const { product_id, weight, variant_name } = payload;
    if (product_id == null || product_id == undefined)
      return requiredParams(res, "product_id is required!");
    if (variant_name == null || variant_name == undefined)
      return requiredParams(res, "variant_name is required!");
    if (weight == null || weight == undefined)
      return requiredParams(res, "weight is required!");
    try {
      const [result] = await createVariantService(payload);
      console.log("result", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async updateVariant(req, res) {
    const payload = req.body;
    //console.log('updateVariant payload', payload);
    const { variant_id, variant_name, variant_sku, weight } = payload;
    if (variant_id == null || variant_id == undefined)
      return requiredParams(res, "variant_id is required!");
    if (variant_name == null || variant_name == undefined)
      return requiredParams(res, "variant_name is required!");
    if (variant_sku == null || variant_sku == undefined)
      return requiredParams(res, "variant_sku is required!");
    if (weight == null || weight == undefined)
      return requiredParams(res, "weight is required!");
    try {
      const [result] = await updateVariantService(payload);
      //console.log('result', result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getVariants(req, res) {
    const payload = req.body;
    console.log("getProducts payload", payload);
    const [result, meta] = await getVariantsService(payload);
    try {
      console.log("result", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Variant list" : "No variants",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async popularProducts(req, res) {
    const payload = req.body;
    // payload check here
    //console.log('getProducts payload', payload);
    const { brand_id } = payload;
    if (brand_id == null || brand_id == undefined)
      return requiredParams(res, "brand_id is required!");
    try {
      const [result, meta] = await popularProductsService(payload);
      if (result) {
        return res.status(200).send({
          success: true,
          msg:
            result.length !== 0
              ? "PopularProducts list by brand"
              : "No products",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async validateDiscount(req, res) {
    const payload = req.body;
    //console.log('validateDiscount payload', payload);
    const { coupon_code, sub_total } = payload;
    if (coupon_code == null || coupon_code == undefined)
      return requiredParams(res, "coupon code is required!");
    if (sub_total == null || sub_total == undefined)
      return requiredParams(res, "sub_total is required!");
    try {
      const [result] = await validateDiscountService(payload);
      console.log("result", result);
      console.log("result[0].min_amount", typeof result[0].min_amount);
      if (result) {
        if (result[0].min_amount) {
          if (Number(result[0].min_amount) > sub_total) {
            return res.status(200).send({
              success: false,
              msg: "subtotal must be greater than minimun order amount",
              data: { min_amount: result[0].min_amount },
            });
          } else {
            if (sub_total < Number(result[0].discount)) {
              return res.status(200).send({
                success: false,
                msg: "subtotal must be greater than discount",
                data: {},
              });
            }
            return res.status(200).send({
              success: true,
              msg: "Valid discount coupon.",
              data: result[0],
            });
          }
          // }
          // if (result[0].variant_id) {
          //   return res.status(200).send({
          //     success: true,
          //     msg: 'Valid discount coupon.',
          //     data: result[0],
          //   });
          // } else if (result[0].category_id) {
          //   console.log('min_amount', result[0].min_amount);
          //   console.log('sub', sub_total.toFixed(2));
          //   if (result[0].min_amount > sub_total.toFixed(2) ) {
          //     return res.status(200).send({
          //       success: false,
          //       msg: 'subtotal must be smaller than upto amount',
          //       data: {},
          //     });
          //   } else {
          //     return res.status(200).send({
          //       success: true,
          //       msg: 'Valid discount coupon.',
          //       data: result[0],
          //     });
          //   }
          //  else if (result[0].discount > 0) {
          //     if (sub_total < result[0].discount) {
          //       return res.status(200).send({
          //         success: false,
          //         msg: 'subtotal must be greater than discount',
          //         data: {},
          //       });
          //     }
          //     return res.status(200).send({
          //       success: true,
          //       msg: 'Valid discount coupon.',
          //       data: result[0],
          //     });
        } else {
          return res.status(200).send({
            success: false,
            msg: "Coupon is not valid!",
            data: {},
          });
        }
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async uploadProductImage(req, res) {
    const { product_id, variant_id } = req.body;

    try {
      // Get file extension
      const fileExtension = req.file.originalname
        .split(".")
        .pop()
        .toLowerCase();
      const filename = uuidv4() + "." + fileExtension;

      // Determine container based on environment
      const containerName =
        process.env.NODE_ENV === "PRODUCTION"
          ? process.env.AZURE_PROD_CONTAINER
          : process.env.AZURE_STAG_CONTAINER;

      const containerClient =
        blobServiceClient.getContainerClient(containerName);
      const blobName = `Products/Product-${product_id}/Variant-${variant_id}/${filename}`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      // Upload to Azure Blob Storage
      await blockBlobClient.uploadStream(
        fs.createReadStream(req.file.path),
        undefined,
        undefined,
        {
          blobHTTPHeaders: {
            blobContentType: `image/${
              fileExtension === "jpg" ? "jpeg" : fileExtension
            }`,
          },
        }
      );

      // Clean up temp file
      fs.unlinkSync(req.file.path);

      // Construct the public URL
      const locationUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT}.blob.core.windows.net/${containerName}/${blobName}`;

      console.log("data", { Location: locationUrl });
      console.log("locationUrl", locationUrl);
      const request = { product_id, variant_id, location: locationUrl };

      try {
        const [result] = await uploadImageService(request);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: `${result.msg}`,
            data: {
              image_id: result.image_id,
              url: result.url,
            },
          });
        }
      } catch (error) {
        return res.status(200).send({
          success: false,
          msg: error.message || `Something went wrong`,
          data: [],
        });
      }
    } catch (error) {
      // Clean up temp file on error
      if (req.file) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (unlinkError) {
          console.log("Error cleaning up temp file:", unlinkError);
        }
      }

      console.log(
        "Error occurred while trying to upload to Azure Blob Storage",
        error
      );
      return res.status(200).send({
        success: false,
        msg: `Error occurred while trying to upload to Azure Blob Storage`,
        data: error,
      });
    }
  }

  static async deleteProductImage(req, res) {
    try {
      const { product_image_id } = req.body;

      if (!product_image_id) {
        return res.status(400).send({
          success: false,
          msg: "product_image_id is required",
          data: [],
        });
      }

      // Get image URL from database
      const [[imageRecord]] = await db
        .promise()
        .query(`SELECT images as url FROM product_images WHERE id = ?`, [
          product_image_id,
        ]);

      if (!imageRecord?.url) {
        return res.status(200).send({
          success: false,
          msg: "No image found in database",
          data: {},
        });
      }

      // Delete from Azure Blob Storage
      const containerName =
        process.env.NODE_ENV === "PRODUCTION"
          ? process.env.AZURE_PROD_CONTAINER
          : process.env.AZURE_STAG_CONTAINER;

      const containerClient =
        blobServiceClient.getContainerClient(containerName);
      
      // Extract blob name from URL
      const urlParts = new URL(imageRecord.url);
      let blobName = urlParts.pathname.substring(1); // Remove leading '/'

      // Remove container name if it's part of the path
      if (blobName.startsWith(containerName + "/")) {
        blobName = blobName.substring(containerName.length + 1);
      }

      await containerClient.deleteBlob(blobName);

      // Delete from database
      await deleteImageService({ product_image_id });

      return res.status(200).send({
        success: true,
        msg: "Image deleted successfully",
        data: imageRecord.url,
      });
    } catch (error) {
      console.log('Error deleting image:', error);
      return res.status(200).send({
        success: false,
        msg: "Failed to delete image: " + error.message,
        data: []
      });
    }
  }

  static async newInStock(req, res) {
    const payload = req.body;
    //console.log('newInStock payload', payload);
    const { brand_id } = payload;
    if (brand_id == null || brand_id == undefined)
      return requiredParams(res, "brand_id is required!");
    const [result, meta] = await newInStockService(payload);
    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "New Stock Product list" : "No new stock",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async uploadinventory(req, res) {
    console.log("req", req.file.fieldname);
    const type = req.file.fieldname;
    const filename = uuidv4() + ".csv";

    // Azure Blob Storage configuration
    const containerName =
      process.env.NODE_ENV === "PRODUCTION"
        ? process.env.AZURE_PROD_CONTAINER
        : process.env.AZURE_STAG_CONTAINER;

    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobName =
      (type == "inventory" ? `Inventory/` : `Store_Inventory/`) + `${filename}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    try {
      // Upload to Azure Blob Storage
      await blockBlobClient.uploadStream(
        fs.createReadStream(req.file.path),
        undefined,
        undefined,
        {
          blobHTTPHeaders: {
            blobContentType: "text/csv",
          },
        }
      );

      // Clean up temp file
      fs.unlinkSync(req.file.path);

      // Construct the public URL
      const locationUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT}.blob.core.windows.net/${containerName}/${blobName}`;

      console.log("locationUrl", locationUrl);
      const request = { location: locationUrl };

      try {
        const [result] = await (type == "inventory"
          ? uploadInventoryService(request)
          : uploadStoreService(request));
        console.log("result", [result]);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: `Uploaded!!`,
            data:
              type == "inventory"
                ? {
                    inventory_id: result.inventory_id,
                    url: result.url,
                  }
                : {
                    store_inventory_id: result.store_inventory_id,
                    url: result.url,
                  },
          });
        }
      } catch (error) {
        return res.status(200).send({
          success: false,
          msg: error.message || `Something went wrong`,
          data: [],
        });
      }
    } catch (error) {
      // Clean up temp file on error
      if (req.file) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (unlinkError) {
          console.log("Error cleaning up temp file:", unlinkError);
        }
      }

      console.log(
        "Error occurred while trying to upload to Azure Blob Storage",
        error
      );
      return res.status(200).send({
        success: false,
        msg: `Error occurred while trying to upload to Azure Blob Storage`,
        data: error,
      });
    }
  }

  static async bulkInventory(req, res) {
    console.log("req", req.body);
    const path = req.body.inventory;
    try {
      // csvtojson({
      //   noheader: false,
      //   headers: ['category', 'sub_category', 'product_name', 'variant_name', 'weight', 'flavour', 'cost_price', 'mrp', 'discount_price', 'stock', 'variant_sku', 'junq_id',]
      // }).fromFile(req.body.inventory).then(function (source) {
      // Create axios stream for CSV processing (replacing deprecated request)
      const response = await axios({
        method: "GET",
        url: path,
        responseType: "stream",
      });

      csvtojson({
        noheader: false,
        ignoreEmpty: true,
        headers: [
          "category",
          "sub_category",
          "sub_category_group_name",
          "product_name",
          "description",
          "variant_name",
          "weight",
          "variant_sku",
          "junq_id",
        ],
      })
        .fromStream(response.data)
        .then((source) => {
          function sub_func() {
            return new Promise(function async(resolve, reject) {
              source.map((m, i) => {
                const sql = `select s.id as subcategory_id,s.name as sub_name,c.id as category_id from category c left join sub_category s on s.name = ${JSON.stringify(
                  m.sub_category
                )}  where c.name = ${JSON.stringify(m.category)} limit 1`;
                db.query(sql, async (error, subresponse) => {
                  if (error) {
                    console.log("error", error);
                  } else {
                    if (
                      subresponse.length == 0 ||
                      subresponse[0].subcategory_id == null
                    ) {
                      console.log("insert", m.sub_category);
                      console.log("category", subresponse[0].category_id);
                      const procedure = `CALL spCreateSubCategory(?)`;
                      console.log("after subcategory create");
                      await db
                        .promise()
                        .query(
                          procedure,
                          JSON.stringify({
                            name: m.sub_category,
                            category_id: subresponse[0].category_id,
                          }),
                          (error, response) => {
                            if (error) {
                              console.log("error", error);
                            } else {
                              return response;
                            }
                          }
                        )
                        .then((response) => {
                          const arr = response[0];
                          const b = arr[0];
                          console.log("id", b[0].sub_category_id);
                          console.log(
                            "sub_category_group_name create",
                            m.sub_category_group_name
                          );
                          if (
                            JSON.stringify(m.sub_category_group_name) !=
                            undefined
                          ) {
                            const procedure = `CALL spCreateSubCategoryGroup(?)`;
                            db.promise().query(
                              procedure,
                              JSON.stringify({
                                name: m.sub_category_group_name,
                                sub_category_id: b[0].sub_category_id,
                              })
                            );
                          }
                        });
                    } else {
                      console.log("exist ", m.sub_category);
                      console.log(
                        "sub_category_group_name update",
                        m.sub_category_group_name
                      );
                      if (
                        JSON.stringify(m.sub_category_group_name) != undefined
                      ) {
                        const sql = `select s.id as sub_category_group_id from sub_category_group s where s.name = ${JSON.stringify(
                          m.sub_category_group_name
                        )}  and s.sub_category_id = ${JSON.stringify(
                          response[0].subcategory_id
                        )} limit 1`;
                        db.query(sql, async (error, response) => {
                          if (error) {
                            console.log("error", error);
                          } else {
                            if (
                              response.length == 0 ||
                              response[0].sub_category_group_id == null
                            ) {
                              const procedure = `CALL spCreateSubCategoryGroup(?)`;
                              db.promise().query(
                                procedure,
                                JSON.stringify({
                                  name: m.sub_category_group_name,
                                  sub_category_id:
                                    subresponse[0].subcategory_id,
                                })
                              );
                            } else {
                              const procedure = `CALL spUpdateSubCategoryGroup(?)`;
                              await db
                                .promise()
                                .query(
                                  procedure,
                                  JSON.stringify({
                                    type: "inventory",
                                    name: m.sub_category_group_name,
                                    sub_category_group_id:
                                      response[0].sub_category_group_id,
                                  })
                                );
                            }
                          }
                        });
                      }
                    }
                  }
                });
                resolve();
              });
            });
          }

          async function prod_func() {
            const j_id = await source.map((o) => {
              return o.junq_id.substr(0, o.junq_id.lastIndexOf("_"));
            });
            const filterid = await source.filter(
              ({ junq_id }, index) =>
                !j_id.includes(
                  junq_id.substr(0, junq_id.lastIndexOf("_")),
                  index + 1
                )
            );
            // console.log('filterid', filterid);
            let count = 0;

            await filterid.map((m, i) => {
              const prod_id = m.junq_id.substr(0, m.junq_id.lastIndexOf("_"));
              const sql = ` select p.* from product_variant p where p.ref_id like concat(${JSON.stringify(
                prod_id
              )},'%') order by p.id ASC limit 1`;
              db.query(sql, async (error, response) => {
                if (error) {
                  console.log("error", error);
                } else {
                  console.log(" product response", response);
                  if (response.length == 0) {
                    db.query(
                      `select scg.id from sub_category_group scg where scg.name = ${JSON.stringify(
                        m.sub_category_group_name
                      )} `,
                      (err, res) => {
                        console.log("while create", res);
                      }
                    );
                    const sql = `select s.id as sub_category_id,s.name as sub_name,ct.id as category_id,scg.id as sub_category_group_id
                       from category ct
                       left join sub_category s on s.name = ${JSON.stringify(
                         m.sub_category
                       )}
                       left join sub_category_group scg on scg.name = ${JSON.stringify(
                         m.sub_category_group_name
                       )}
                       where ct.name = ${JSON.stringify(m.category)} limit 1`;
                    db.query(sql, async (error, res) => {
                      console.log("product payload", res);
                      if (error) {
                        console.log("error", error);
                      } else {
                        console.log(" id response", res);
                        const payload = {
                          name: m.product_name,
                          category_id: res[0].category_id,
                          sub_category_id: res[0].sub_category_id,
                          sub_category_group_id:
                            res[0].sub_category_group_id == undefined
                              ? null
                              : res[0].sub_category_group_id,
                          description: m.description,
                        };
                        const [result] = await createProductService(payload);
                        for (let i = 0; i < source.length; i++) {
                          if (
                            source[i].junq_id.substr(
                              0,
                              source[i].junq_id.lastIndexOf("_")
                            ) == m.junq_id.substr(0, m.junq_id.lastIndexOf("_"))
                          ) {
                            const varpayload = {
                              ref_id: source[i].junq_id,
                              product_id: result.product_id,
                              variant_name: source[i].variant_name,
                              variant_sku: source[i].variant_sku,
                              // price: source[i].discount_price == undefined ? 0 : source[i].discount_price,
                              // cost_price: source[i].cost_price == undefined ? 0 : source[i].cost_price,
                              // mrp: source[i].mrp == undefined ? 0 : source[i].mrp,
                              weight:
                                source[i].weight == undefined
                                  ? 0
                                  : source[i].weight,
                              // variant_stock: source[i].stock == undefined ? 0 : source[i].stock
                            };
                            await createVariantService(varpayload);
                          }
                        }
                      }
                    });
                  } else {
                    const sql = `select s.id as sub_category_id,s.name as sub_name,ct.id as category_id,scg.id as sub_category_group_id
                        from category ct
                        left join sub_category s on s.name = ${JSON.stringify(
                          m.sub_category
                        )}
                        left join sub_category_group scg on scg.name = ${JSON.stringify(
                          m.sub_category_group_name
                        )}
                        where ct.name = ${JSON.stringify(m.category)} limit 1`;
                    db.query(sql, async (error, res) => {
                      console.log("product payload", res);
                      if (error) {
                        console.log("error", error);
                      } else {
                        const update = `UPDATE products p SET p.name 	= ${JSON.stringify(
                          m.product_name
                        )},p.description = ${JSON.stringify(
                          m.description == undefined ? null : m.description
                        )},
                            p.category_id   = ${JSON.stringify(
                              res[0].category_id
                            )},
                            p.sub_category_id = ${JSON.stringify(
                              res[0].res[0].sub_category_id
                            )},
                             p.sub_category_group_id = ${JSON.stringify(
                               res[0].sub_category_group_id == undefined
                                 ? null
                                 : res[0].sub_category_group_id
                             )},
                            ,p.updated_at = NOW() WHERE p.id = ${
                              response[0].product_id
                            }`;
                        db.query(update, async (error, response) => {
                          console.log("product update", error || response);
                        });
                      }
                    });
                    for (let i = 0; i < source.length; i++) {
                      if (
                        source[i].junq_id.substr(
                          0,
                          source[i].junq_id.lastIndexOf("_")
                        ) == m.junq_id.substr(0, m.junq_id.lastIndexOf("_"))
                      ) {
                        const varpayload = {
                          ref_id: source[i].junq_id,
                          product_id: response[0].product_id,
                          variant_name:
                            source[i].variant_name == undefined
                              ? null
                              : source[i].variant_name,
                          variant_sku:
                            source[i].variant_sku == undefined
                              ? null
                              : source[i].variant_sku,
                          // price: source[i].discount_price == undefined ? 0 : source[i].discount_price,
                          // cost_price: source[i].cost_price == undefined ? 0 : source[i].cost_price,
                          // mrp: source[i].mrp == undefined ? 0 : source[i].mrp,
                          weight:
                            source[i].weight == undefined
                              ? 0
                              : source[i].weight,
                          // variant_stock: source[i].stock == undefined ? 0 : source[i].stock
                        };
                        await createVariantService(varpayload);
                      }
                    }
                  }
                  count = count + 1;
                  console.log(`count ${count}`, filterid.length);
                  if (count == filterid.length) {
                    return res.status(200).send({
                      success: true,
                      msg: `Bulk Inventory successfull !! `,
                      data: {},
                    });
                  }
                }
              });
            });
          }

          sub_func().then(prod_func);
        });
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  //   static async getGofrugalCategories(req, res) {

  //     try {

  //         const options = {
  //           'method': 'GET',
  //           'url': `http://1520028.true-order.com/WebReporter/api/v1/categories`,
  //           'headers': {

  //             'Content-Type': 'application/json',
  //             'X-Auth-Token':'47175886E0B7A00B965A1CD68F63D470BD2352BAC2C168470ECA9CCD739560721C5E2A3D181164D0'
  //           },
  //         };

  //         const result = await axios(options);
  //         console.log('result ', result.data);

  //         const items=result.data.items;

  // // console.log('items :>> ', items);

  //         for(let item of items){
  //         const procedure = `CALL spGetGofrugalItems(?)`;
  //         await db.promise().query(procedure, JSON.stringify(item), (error, response) => {
  //           console.log('response', response);
  //           console.log('error', error);
  //         });
  //       }
  //         res.status(200).send({

  //           msg: `data available`,
  //           data: result.data

  //         })

  //       } catch (error) {
  //         console.log('error', error);
  //       }
  //     }

  static async getGofrugalItems() {
    const date = new Date();
    console.log(`started  :>> ${date}`);

    setInterval(async () => {
      try {
        const sql = "select domainpath,token from gofrugal where id=1";

        const [[{ domainpath, token }]] = await db.promise().query(sql);

        const tsql =
          "select max(ifnull(itemTimestamp ,0)) as maxtimestamp from product_variant;";

        const [[{ maxtimestamp }]] = await db.promise().query(tsql);

        const options = {
          method: "GET",
          url: `${domainpath}/api/v1/items?q=itemTimeStamp>=${maxtimestamp}&limit=10000`,
          headers: {
            "Content-Type": "application/json",
            "X-Auth-Token": token,
          },
        };

        const result = await axios(options);
        console.log("result ", result.data);

        const items = result.data.items;

        // console.log('items :>> ', items);

        for (let item of items) {
          console.log(" :>> ", item.itemId);
          const procedure = `CALL spGetGofrugalItems(?)`;
          await db
            .promise()
            .query(procedure, JSON.stringify(item), (error, response) => {
              console.log("response", response);
              console.log("error", error);
            });
        }

        console.log(` ${items.length} items updated`);
        // res.status(200).send({

        //   msg: `data available`,
        //   data: result.data

        // })
      } catch (error) {
        console.log("error", error);
      }
    }, 600000);
  }

  static async storeVariants(req, res) {
    console.log("req", req.body);
    const path = req.body.store_inventory;
    try {
      // Create axios stream for CSV processing (replacing deprecated request)
      const response = await axios({
        method: "GET",
        url: path,
        responseType: "stream",
      });

      csvtojson({
        noheader: false,
        // ignoreEmpty: true,
        headers: [
          "category",
          "sub_category",
          "product_name",
          "description",
          "variant_name",
          "weight",
          "variant_sku",
          "cost_price",
          "mrp",
          "price",
          "stock",
          "is_active",
          "junq_id",
          "store_id",
        ],
      })
        .fromStream(response.data)
        .subscribe(
          (json) => {
            // return new Promise((resolve, reject) => {
            const procedure = `CALL spStoreMapping(?)`;
            db.promise().query(
              procedure,
              JSON.stringify(json),
              (error, response) => {
                console.log("response", response);
                console.log("error", error);
              }
            );
            // })
          },
          (err) => {
            console.error("Error:", err);
            return res.status(200).send({
              success: false,
              msg: `Bulk Inventory error !! `,
              data: {},
            });
          },
          (success) => {
            console.log("Success");
            return res.status(200).send({
              success: true,
              msg: `Bulk Inventory successfull !! `,
              data: {},
            });
          }
        );
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }
  
}

module.exports = ProductController;
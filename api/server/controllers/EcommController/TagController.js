const {
  createTagService,
  updateTagService,
  getTagService,
} = require("../../services/EcommServices/TagService");
const { requiredParams } = require("../../utility/requiredCheck");

class TagController {
  static async createTag(req, res) {
    const payload = req.body;
    const { name } = payload;

    //console.log('createCategory payload', payload);
    if (name == null || name == undefined)
      return requiredParams(res, "name is required!");

    const [result] = await createTagService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {
            tag_id: result.tag_id,
          },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async updateTag(req, res) {
    const payload = req.body;
    const { name, description, tag_id } = payload;

    //console.log('updateTag payload', payload);
    if (name == null || name == undefined)
      return requiredParams(res, "name is required!");

    // if (description == null || description == undefined) return requiredParams(res, 'description is required!');
    if (tag_id == null || tag_id == undefined)
      return requiredParams(res, "tag_id is required!");

    const [result] = await updateTagService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getTag(req, res) {
    const payload = req.body;

    //console.log('getTag payload', payload);
    const [result, meta] = await getTagService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Tag List" : "No Tag",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }
}

module.exports = TagController;

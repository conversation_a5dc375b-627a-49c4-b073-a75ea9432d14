const { requiredParams } = require("../../utility/requiredCheck");
const fs = require("fs");
const { BlobServiceClient } = require("@azure/storage-blob");
const { createReadStream, unlinkSync } = require("fs");
const { v4: uuidv4 } = require("uuid");
const db = require("../../../config/connection");
const { uploadImageService, deleteImageService} = require("../../services/EcommServices/ProductService");
const {deleteUserImageService } = require("../../services/UserService/UserService");
const {
  createOfferService,
  updateOfferService,
  getOfferService,
  getHomePageOfferService,
  createBannerService,
  getBannerService,
  updateBannerService,
} = require("../../services/EcommServices/OfferService");


// Initialize Azure Storage client
const blobServiceClient = BlobServiceClient.fromConnectionString(
  process.env.AZURE_STORAGE_CONNECTION_STRING
);

class OfferController {
  static async createOffer(req, res) {
    const payload = req.body;
    const {
      name,
      coupon_code,
      valid_from,
      valid_till,
      is_percent,
      percent_discount,
      amount_discount,
    } = payload;

    //console.log('createCategory payload', payload);
    if (name == null || name == undefined)
      return requiredParams(res, "name is required!");

    if (is_percent == 1) {
      if (percent_discount == null || percent_discount == undefined)
        return requiredParams(res, "percent_discount is required!");
    }

    if (is_percent == 0) {
      if (amount_discount == null || amount_discount == undefined)
        return requiredParams(res, "amount_discount is required!");
    }

    if (coupon_code == null || coupon_code == undefined)
      return requiredParams(res, "coupon_code is required!");

    if (valid_from == null || valid_from == undefined)
      return requiredParams(res, "valid_from is required!");

    if (valid_till == null || valid_till == undefined)
      return requiredParams(res, "valid_till is required!");

    const [result] = await createOfferService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: [],
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async updateOffer(req, res) {
    const payload = req.body;
    const {
      name,
      description,
      coupon_code,
      valid_from,
      valid_till,
      is_percent,
      percent_discount,
      amount_discount,
    } = payload;

    //console.log('updateOffer payload', payload);
    if (name == null || name == undefined)
      return requiredParams(res, "name is required!");

    // if (description == null || description == undefined) return requiredParams(res, 'description is required!');
    if (is_percent == 1) {
      if (percent_discount == null || percent_discount == undefined)
        return requiredParams(res, "percent_discount is required!");
    }

    if (is_percent == 0) {
      if (amount_discount == null || amount_discount == undefined)
        return requiredParams(res, "amount_discount is required!");
    }

    if (coupon_code == null || coupon_code == undefined)
      return requiredParams(res, "coupon_code is required!");

    if (valid_from == null || valid_from == undefined)
      return requiredParams(res, "valid_from is required!");

    if (valid_till == null || valid_till == undefined)
      return requiredParams(res, "valid_till is required!");

    const [result] = await updateOfferService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: [],
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getOffer(req, res) {
    const payload = req.body;

    //console.log('getOffer payload', payload);
    const [result, meta] = await getOfferService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Offer List" : "No Offer",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getHomePageOffer(req, res) {
    const payload = req.body;

    //console.log('getOffer payload', payload);
    const [result, meta] = await getHomePageOfferService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Home Page Offer List" : "No Offer",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async uploadOfferImage(req, res) {
    const { offer_id } = req.body;

    try {
      // Get file extension
      const fileExtension = req.file.originalname
        .split(".")
        .pop()
        .toLowerCase();
      const filename = uuidv4() + "." + fileExtension;

      // Determine container based on environment
      const containerName =
        process.env.NODE_ENV === "PRODUCTION"
          ? process.env.AZURE_PROD_CONTAINER
          : process.env.AZURE_STAG_CONTAINER;

      const containerClient =
        blobServiceClient.getContainerClient(containerName);
      const blobName = `Offers/Offer-${offer_id}/${filename}`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      // Upload to Azure Blob Storage
      await blockBlobClient.uploadStream(
        fs.createReadStream(req.file.path),
        undefined,
        undefined,
        {
          blobHTTPHeaders: {
            blobContentType: `image/${
              fileExtension === "jpg" ? "jpeg" : fileExtension
            }`,
          },
        }
      );

      // Clean up temp file
      fs.unlinkSync(req.file.path);

      // Construct the public URL
      const locationUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT}.blob.core.windows.net/${containerName}/${blobName}`;

      console.log("data", { Location: locationUrl });
      console.log("locationUrl", locationUrl);
      const request = { offer_id, location: locationUrl };

      try {
        const [result] = await uploadImageService(request);

        if (result) {
          return res.status(200).send({
            success: true,
            msg: `${result.msg}`,
            data: {
              image_id: result.image_id,
              url: result.url,
            },
          });
        }
      } catch (error) {
        return res.status(200).send({
          success: false,
          msg: error.message || `Something went wrong`,
          data: [],
        });
      }
    } catch (error) {
      // Clean up temp file on error
      if (req.file) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (unlinkError) {
          console.log("Error cleaning up temp file:", unlinkError);
        }
      }

      console.log(
        "Error occurred while trying to upload to Azure Blob Storage",
        error
      );
      return res.status(200).send({
        success: false,
        msg: `Error occurred while trying to upload to Azure Blob Storage`,
        data: error,
      });
    }
  }

  static async deleteOfferImage(req, res) {
    try {
      const { offer_image_id } = req.body;

      if (!offer_image_id) {
        return res.status(400).send({
          success: false,
          msg: "offer_image_id is required",
          data: [],
        });
      }

      // Get image URL from database
      const [[imageRecord]] = await db
        .promise()
        .query(`SELECT original_url as url FROM offer_image WHERE id = ?`, [
          offer_image_id,
        ]);

      if (!imageRecord?.url) {
        return res.status(200).send({
          success: false,
          msg: "No image found in database",
          data: {},
        });
      }

      // Delete from Azure Blob Storage
      const containerName =
        process.env.NODE_ENV === "PRODUCTION"
          ? process.env.AZURE_PROD_CONTAINER
          : process.env.AZURE_STAG_CONTAINER;

      const containerClient =
        blobServiceClient.getContainerClient(containerName);

      const urlParts = new URL(imageRecord.url);
      let blobName = urlParts.pathname.substring(1);

      if (blobName.startsWith(containerName + "/")) {
        blobName = blobName.substring(containerName.length + 1);
      }

      await containerClient.deleteBlob(blobName);

      // Delete from database
      await deleteUserImageService({ offer_image_id });

      return res.status(200).send({
        success: true,
        msg: "Image deleted successfully",
        data: imageRecord.url,
      });
    } catch (error) {
      console.log("Error deleting image:", error);
      return res.status(200).send({
        success: false,
        msg: "Failed to delete image: " + error.message,
        data: [],
      });
    }
  }

  static async createBanner(req, res) {
    const payload = req.body;
    const { type, product_id, url, category_id, sub_category_id } = payload;

    if (type == null || type == undefined)
      return requiredParams(res, "type is required!");

    if (type == "url") {
      if (url == null || url == undefined)
        return requiredParams(res, "url is required!");
    }

    if (type == "product") {
      if (product_id == null || product_id == undefined)
        return requiredParams(res, "product_id is required!");
    }

    if (type == "category") {
      if (category_id == null || category_id == undefined)
        return requiredParams(res, "category_id is required!");
    }

    if (type == "sub_category") {
      if (sub_category_id == null || sub_category_id == undefined)
        return requiredParams(res, "sub_category_id is required!");
    }

    const [result] = await createBannerService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: `${result.banner_id}`,
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async updateBanner(req, res) {
    const payload = req.body;
    const { type, banner_id, url, product_id, category_id, sub_category_id } =
      payload;

    if (banner_id == null || banner_id == undefined)
      return requiredParams(res, "banner_id is required!");

    if (type == null || type == undefined)
      return requiredParams(res, "type is required!");

    if (type == "url") {
      if (url == null || url == undefined)
        return requiredParams(res, "url is required!");
    }

    if (type == "product") {
      if (product_id == null || product_id == undefined)
        return requiredParams(res, "product_id is required!");
    }

    if (type == "category") {
      if (category_id == null || category_id == undefined)
        return requiredParams(res, "category_id is required!");
    }

    if (type == "sub_category") {
      if (sub_category_id == null || sub_category_id == undefined)
        return requiredParams(res, "sub_category_id is required!");
    }

    const [result] = await updateBannerService(payload);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: [],
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async uploadBannerImage(req, res) {
    const { banner_id } = req.body;

    try {
      // Get file extension
      const fileExtension = req.file.originalname
        .split(".")
        .pop()
        .toLowerCase();
      const filename = uuidv4() + "." + fileExtension;

      // Determine container based on environment
      const containerName =
        process.env.NODE_ENV === "PRODUCTION"
          ? process.env.AZURE_PROD_CONTAINER
          : process.env.AZURE_STAG_CONTAINER;

      const containerClient =
        blobServiceClient.getContainerClient(containerName);
      const blobName = `Banners/Banner-${banner_id}/${filename}`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      // Upload to Azure Blob Storage
      await blockBlobClient.uploadStream(
        fs.createReadStream(req.file.path),
        undefined,
        undefined,
        {
          blobHTTPHeaders: {
            blobContentType: `image/${
              fileExtension === "jpg" ? "jpeg" : fileExtension
            }`,
          },
        }
      );

      // Clean up temp file
      fs.unlinkSync(req.file.path);

      // Construct the public URL
      const locationUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT}.blob.core.windows.net/${containerName}/${blobName}`;

      console.log("data", { Location: locationUrl });
      console.log("locationUrl", locationUrl);
      const request = { banner_id, location: locationUrl };

      try {
        const [result] = await uploadImageService(request);

        if (result) {
          return res.status(200).send({
            success: true,
            msg: `${result.msg}`,
            data: {
              image_id: result.image_id,
              url: result.url,
            },
          });
        }
      } catch (error) {
        return res.status(200).send({
          success: false,
          msg: error.message || `Something went wrong`,
          data: [],
        });
      }
    } catch (error) {
      // Clean up temp file on error
      if (req.file) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (unlinkError) {
          console.log("Error cleaning up temp file:", unlinkError);
        }
      }

      console.log(
        "Error occurred while trying to upload to Azure Blob Storage",
        error
      );
      return res.status(200).send({
        success: false,
        msg: `Error occurred while trying to upload to Azure Blob Storage`,
        data: error,
      });
    }
  }

  static async deleteBannerImage(req, res) {
    try {
      const { banner_image_id } = req.body;

      if (!banner_image_id) {
        return res.status(400).send({
          success: false,
          msg: "banner_image_id is required",
          data: [],
        });
      }

      // Get image URL from database
      const [[imageRecord]] = await db
        .promise()
        .query(`SELECT images as url FROM banner_images WHERE id = ?`, [
          banner_image_id,
        ]);

      if (!imageRecord?.url) {
        return res.status(200).send({
          success: false,
          msg: "No image found in database",
          data: {},
        });
      }

      // Delete from Azure Blob Storage
      const containerName =
        process.env.NODE_ENV === "PRODUCTION"
          ? process.env.AZURE_PROD_CONTAINER
          : process.env.AZURE_STAG_CONTAINER;

      const containerClient =
        blobServiceClient.getContainerClient(containerName);

      const urlParts = new URL(imageRecord.url);
      let blobName = urlParts.pathname.substring(1);

      if (blobName.startsWith(containerName + "/")) {
        blobName = blobName.substring(containerName.length + 1);
      }

      await containerClient.deleteBlob(blobName);

      // Delete from database
      await deleteImageService({ banner_image_id });

      return res.status(200).send({
        success: true,
        msg: "Image deleted successfully",
        data: imageRecord.url,
      });
    } catch (error) {
      console.log("Error deleting image:", error);
      return res.status(200).send({
        success: false,
        msg: "Failed to delete image: " + error.message,
        data: [],
      });
    }
  }

  static async getBanner(req, res) {
    const payload = req.body;
    const [result, meta] = await getBannerService(payload);

    console.log("log", result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Banner List" : "No Banner",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

}

module.exports = OfferController;

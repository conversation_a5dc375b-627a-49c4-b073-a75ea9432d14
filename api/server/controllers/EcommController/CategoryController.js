const { requiredParams } = require("../../utility/requiredCheck");
const db = require("../../../config/connection");
const { BlobServiceClient } = require("@azure/storage-blob");
const { createReadStream, unlinkSync } = require("fs");
const { v4: uuidv4 } = require("uuid");
const fs = require("fs");
const {
  uploadImageService,
  deleteImageService,
} = require("../../services/EcommServices/ProductService");
const {
  createCategoryService,
  updateCategoryService,
  getCategoryService,
} = require("../../services/EcommServices/CategoryService");

const blobServiceClient = BlobServiceClient.fromConnectionString(
  process.env.AZURE_STORAGE_CONNECTION_STRING
);

class CategoryController {
  
  static async createCategory(req, res) {
    const payload = req.body;
    const { name, description } = payload;

    //console.log('createCategory payload', payload);
    if (name == null || name == undefined)
      return requiredParams(res, "name is required!");

    const [result] = await createCategoryService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: { category_id: result.category_id },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async updateCategory(req, res) {
    const payload = req.body;
    const { category_id, name, description } = payload;

    if (category_id == null || category_id == undefined)
      return requiredParams(res, "category_id is required!");

    if (name == null || name == undefined)
      return requiredParams(res, "name is required!");

    if (description === undefined)
      return requiredParams(res, "description is required!");

    //console.log('updateCategory payload', payload);
    const [result] = await updateCategoryService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: [],
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getCategory(req, res) {
    const payload = req.body;

    //console.log('createAddress payload', payload);
    const [result, meta] = await getCategoryService(payload);
    //console.log('result', result);

    try {
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.length !== 0 ? "Category List" : "No Category",
          data: result,
          meta: meta[0] || { total: 0 },
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async UploadCategoryImage(req, res) {
    const { category_id } = req.body;

    try {
      // Validation
      if (category_id == null || category_id == undefined) {
        if (req.file) unlinkSync(req.file.path);
        return requiredParams(res, "category_id is required!");
      }

      if (!req.file) {
        return requiredParams(res, "Image file is required!");
      }

      // File validation
      const fileExtension = req.file.originalname
        .split(".")
        .pop()
        .toLowerCase();
      const allowedExtensions = ["jpg", "jpeg", "png"];

      if (!allowedExtensions.includes(fileExtension)) {
        unlinkSync(req.file.path);
        return res.status(400).send({
          success: false,
          msg: "Invalid file type. Only JPG, JPEG, and PNG are allowed.",
          data: [],
        });
      }

      // Determine container based on environment
      const containerName =
        process.env.NODE_ENV === "PRODUCTION"
          ? process.env.AZURE_PROD_CONTAINER
          : process.env.AZURE_STAG_CONTAINER;

      const containerClient =
        blobServiceClient.getContainerClient(containerName);

      // Generate filename and blob path (same structure as S3)
      const filename = uuidv4() + "." + fileExtension;
      const blobName = `Category/Category-${category_id}/${filename}`;

      // Upload to Azure Blob Storage
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      await blockBlobClient.uploadStream(
        createReadStream(req.file.path),
        undefined,
        undefined,
        {
          blobHTTPHeaders: {
            blobContentType: `image/${
              fileExtension === "jpg" ? "jpeg" : fileExtension
            }`,
          },
        }
      );

      // Clean up temp file
      unlinkSync(req.file.path);

      // Construct the public URL
      const locationUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT}.blob.core.windows.net/${containerName}/${blobName}`;

      // Save to database using existing service
      const request = { category_id, location: locationUrl };
      const [result] = await uploadImageService(request);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {
            image_id: result.image_id,
            url: result.url,
          },
        });
      }
    } catch (error) {
      // Clean up temp file on error
      if (req.file) {
        try {
          unlinkSync(req.file.path);
        } catch (unlinkError) {
          console.log("Error cleaning up temp file:", unlinkError);
        }
      }

      return res.status(200).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: [],
      });
    }
  }

  static async deleteCategoryImage(req, res) {
    try {
      const { category_image_id } = req.body;

      if (!category_image_id) {
        return res.status(400).send({
          success: false,
          msg: "category_image_id is required",
          data: [],
        });
      }

      // Get image URL from database
      const [[imageRecord]] = await db
        .promise()
        .query(`SELECT images as url FROM category_images WHERE id = ?`, [
          category_image_id,
        ]);

      if (!imageRecord?.url) {
        return res.status(200).send({
          success: false,
          msg: "No image found in database",
          data: {},
        });
      }

      // Delete from Azure Blob Storage
      const containerName =
        process.env.NODE_ENV === "PRODUCTION"
          ? process.env.AZURE_PROD_CONTAINER
          : process.env.AZURE_STAG_CONTAINER;

      const containerClient =
        blobServiceClient.getContainerClient(containerName);
      let blobName = new URL(imageRecord.url).pathname.substring(1);

      if (blobName.startsWith(containerName + "/")) {
        blobName = blobName.substring(containerName.length + 1);
      }

      await containerClient.deleteBlob(blobName);

      // Delete from database
      await deleteImageService(req.body);

      return res.status(200).send({
        success: true,
        msg: "Image deleted successfully",
        data: imageRecord.url,
      });
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: "Failed to delete image: " + error.message,
        data: [],
      });
    }
  }

}

module.exports = CategoryController;

const { createCategory, getCategory, updateCategory, UploadCategoryImage, deleteCategoryImage } = require('../controllers/EcommController/CategoryController');
const { createSubCategory, getSubCategory, updateSubCategory, UploadSubCategoryImage, deleteSubCategoryImage } = require('../controllers/EcommController/SubCategoryController');
const { createProduct, updateProduct, productsMayLikes, getProducts, getProductByID, uploadProductImage, deleteProductImage, getProductsByCategory, validateDiscount, updateVariant, createVariant, getVariants, bulkInventory, uploadinventory, storeVariants, getFiveProductsPerCategory, filterProducts } = require('../controllers/EcommController/ProductController');
const { createOrder, getOrders, updateCart, getOrderByID, updateOrder, cancelOrder, Orderstatus,orders, getcart, getGofrugalOrders } = require('../controllers/EcommController/OrderController');
const { createReview, updateReview, getReview, setOrderReview } = require('../controllers/EcommController/ReviewController');
const { initiatePayment, capturedPayment, getPaymentList, getPaymentByID, refundPayment } = require('../controllers/EcommController/PaymentController');
const { updateConfig, getConfig } = require('../controllers/StockConfigController');
const { createOffer, updateOffer, getOffer, getHomePageOffer, uploadOfferImage, deleteOfferImage, createBanner, updateBanner, uploadBannerImage, deleteBannerImage, getBanner } = require('../controllers/EcommController/OfferController');
const { createTag, updateTag, getTag } = require('../controllers/EcommController/TagController');
const { updateWishlist, getWishlist } = require('../controllers/EcommController/WishlistController');
const { createSubCategoryGroup, updateSubCategoryGroup, getSubCategoryGroup, UploadSubCategoryGroupImage } = require('../controllers/EcommController/SubCategoryGroupController');
const { exportCsvReport, exportVaraintCsv } = require('../controllers/EcommController/ReportController');
const { getSubscriptionList } = require('../controllers/EcommController/SubscriptionController');

const { Router } = require('express');
const router = Router();
const multer = require('multer');

const { authorization } = require('../../auth/tokenValidator');

router.post('/createCategory', authorization, createCategory);
router.post('/updateCategory', authorization, updateCategory);
router.post('/getCategory', authorization, getCategory);

router.post('/createSubCategory', authorization, createSubCategory);
router.post('/updateSubCategory', authorization, updateSubCategory);
router.post('/getSubCategory', authorization, getSubCategory);

router.post('/createSubCategoryGroup', authorization, createSubCategoryGroup);
router.post('/updateSubCategoryGroup', authorization, updateSubCategoryGroup);
router.post('/getSubCategoryGroup', authorization, getSubCategoryGroup);


router.post('/createProduct', authorization, createProduct);
router.post('/updateProduct', authorization, updateProduct);
router.post('/productsMayLikes', authorization, productsMayLikes);
router.post('/getProducts', authorization, getProducts);
router.post('/getProductByID', authorization, getProductByID);
router.post('/getProductsByCategory', authorization, getProductsByCategory);
router.post('/createVariant', authorization, createVariant);
router.post('/updateVariant', authorization, updateVariant);
router.post('/getVariants', authorization, getVariants);
router.post('/getFiveProductsPerCategory', authorization, getFiveProductsPerCategory);
router.post('/filterProducts', authorization, filterProducts);

router.post('/createOrder', authorization, createOrder);
router.post('/getOrders', authorization, getOrders);
router.get('store/:storeid/orders/:id?', authorization, orders);

router.post('/getOrderByID', authorization, getOrderByID);
router.post('/updateOrder', authorization, updateOrder);
router.post('/cancelOrder', authorization, cancelOrder);
router.post('/Orderstatus', authorization, Orderstatus);
router.post('/updateCart', authorization, updateCart);
router.post('/getCart', authorization, getcart);


router.post('/createReview', authorization, createReview);
router.post('/updateReview', authorization, updateReview);
router.post('/getReview', authorization, getReview);
router.post('/setOrderReview', authorization, setOrderReview);


router.post('/uploadProductImage', multer({ dest: 'temp/', limits: { fieldSize: 8 * 1024 * 1024 } }).single('product_img'), authorization, uploadProductImage);
router.post('/deleteProductImage', authorization, deleteProductImage);

router.post('/uploadCategoryImage', multer({ dest: 'temp/', limits: { fieldSize: 8 * 1024 * 1024 } }).single('category_img'), authorization, UploadCategoryImage);
router.post('/deleteCategoryImage', authorization, deleteCategoryImage);

router.post('/uploadSubCategoryImage', multer({ dest: 'temp/', limits: { fieldSize: 8 * 1024 * 1024 } }).single('sub_category_img'), authorization, UploadSubCategoryImage);
router.post('/deleteSubCategoryImage', authorization, deleteSubCategoryImage);

router.post('/uploadSubCategoryGroupImage', multer({ dest: 'temp/', limits: { fieldSize: 8 * 1024 * 1024 } }).single('sub_category_group_img'), authorization, UploadSubCategoryGroupImage);
router.post('/deleteSubCategoryGroupImage', authorization, deleteSubCategoryImage);

router.post('/initiatePayment', authorization, initiatePayment);
router.post('/capturedPayment', capturedPayment);
router.post('/getPayments', authorization, getPaymentList);
router.post('/getPaymentByID', authorization, getPaymentByID);
router.post('/refundpayment',authorization,refundPayment)

//gofrugal


// router.post('/getGofrugalOrders',authorization, getGofrugalOrders)


router.post('/updateConfig', authorization, updateConfig);
router.get('/getConfig', getConfig);

router.post('/createOffer', authorization, createOffer);
router.post('/updateOffer', authorization, updateOffer);
router.post('/getOffer', authorization, getOffer);
router.post('/getHomePageOffer', authorization, getHomePageOffer);

router.post('/createBanner', authorization, createBanner);
router.post('/updateBanner', authorization, updateBanner);
router.post('/getBanner', getBanner);

router.post('/uploadBannerImage', multer({ dest: 'temp/', limits: { fieldSize: 8 * 1024 * 1024 } }).single('banner_img'), authorization, uploadBannerImage);
router.post('/deleteBannerImage', authorization, deleteBannerImage);

router.post('/uploadOfferImage', multer({ dest: 'temp/', limits: { fieldSize: 8 * 1024 * 1024 } }).single('offer_img'), authorization, uploadOfferImage);
router.post('/deleteOfferImage', authorization, deleteOfferImage);

router.post('/createTag', authorization, createTag);
router.post('/updateTag', authorization, updateTag);
router.post('/getTag', authorization, getTag);

router.post('/updateWishlist', authorization, updateWishlist);
router.post('/getWishlist', authorization, getWishlist);

router.post('/validateDiscount', authorization, validateDiscount);

router.post('/exportCsvReport', authorization, exportCsvReport);
router.post('/exportVaraintCsv', authorization, exportVaraintCsv);
router.post('/uploadinventory', multer({ dest: 'temp/' }).single('inventory'), authorization, uploadinventory);
router.post('/bulkInventory',authorization, bulkInventory);

router.post('/uploadStoreinventory', multer({ dest: 'temp/' }).single('store_inventory'), authorization, uploadinventory);
router.post('/storeVariants',authorization, storeVariants);
router.post('/getSubscriptionList',authorization, getSubscriptionList);

module.exports = router;

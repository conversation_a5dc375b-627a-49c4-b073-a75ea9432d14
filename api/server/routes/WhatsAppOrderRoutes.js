const { Router } = require('express');
const { authorization } = require('../../auth/tokenValidator');

// Import WhatsApp Order Controllers
const WhatsAppUserController = require('../controllers/WhatsAppOrder/WhatsAppUserController');
const WhatsAppProductController = require('../controllers/WhatsAppOrder/WhatsAppProductController');
const WhatsAppOrderController = require('../controllers/WhatsAppOrder/WhatsAppOrderController');
const WhatsAppAddressController = require('../controllers/WhatsAppOrder/WhatsAppAddressController');

const router = Router();

// ============= WHATSAPP USER MANAGEMENT =============
// User Creation & Registration
router.post('/whatsappUserCreation', WhatsAppUserController.whatsappUserCreation);
router.post('/registerUser', authorization, WhatsAppUserController.registerUser);

// ============= WHATSAPP ADDRESS MANAGEMENT =============
// Address CRUD Operations
router.post('/getAddress', authorization, WhatsAppAddressController.getAddress);
router.post('/createAddress', authorization, WhatsAppAddressController.createAddress);
router.post('/updateAddress', authorization, WhatsAppAddressController.updateAddress);

// ============= WHATSAPP PRODUCT MANAGEMENT =============
// Product Search & Details
router.post('/getProducts', authorization, WhatsAppProductController.getProducts);

// ============= WHATSAPP ORDER MANAGEMENT =============
// Order Operations
router.post('/createOrder', authorization, WhatsAppOrderController.createOrder);
router.post('/updateOrder', authorization, WhatsAppOrderController.updateOrder);
router.post('/getOrderTracking', authorization, WhatsAppOrderController.getOrderTracking);

module.exports = router;

const { Router } = require('express');
const { authorization } = require('../../auth/tokenValidator');

// Import Mobile Controllers
const MobileUserController = require('../controllers/MobileController/MobileUserController');
const MobileProductController = require('../controllers/MobileController/MobileProductController');
const MobileOrderController = require('../controllers/MobileController/MobileOrderController');
const MobilePaymentController = require('../controllers/MobileController/MobilePaymentController');

const router = Router();

// ============= AUTHENTICATION & USER MANAGEMENT =============
// OTP Authentication
router.post('/customer/sendOtp', MobileUserController.sendOtp);
router.post('/customer/verifyOtp', MobileUserController.verifyOtp);
router.post('/customer/register', authorization, MobileUserController.registerUser);

// Profile Management
router.post('/customer/updateProfile', authorization, MobileUserController.updateProfile);

// Store Location
router.post('/customer/getNearbyStore', authorization, MobileUserController.getNearbyStore);

// Address Management
router.post('/customer/createOrUpdateAddress', authorization, MobileUserController.createOrUpdateAddress);
router.post('/customer/deleteAddress', authorization, MobileUserController.deleteAddress);
router.post('/customer/getAddresses', authorization, MobileUserController.getAddresses);

// ============= PRODUCT MANAGEMENT =============
// Product APIs - ALL POST
router.post('/customer/getItem', authorization, MobileProductController.getProductById);
router.post('/customer/getItems', authorization, MobileProductController.getItemsList);

// Category APIs
router.post('/customer/getCategories', authorization, MobileProductController.getCategories);
router.post('/customer/getBanners', authorization, MobileProductController.getBanners);

// Wishlist APIs
router.post('/customer/getWishlist', authorization, MobileProductController.getWishlist);
router.post('/customer/addItemToWishlist', authorization, MobileProductController.addToWishlist);
router.post('/customer/removeItemFromWishlist', authorization, MobileProductController.removeFromWishlist);

// ============= ORDER MANAGEMENT =============
// Cart APIs - ALL POST
router.post('/customer/loadCart', authorization, MobileOrderController.loadCart);
router.post('/customer/getOffers', authorization, MobileOrderController.getOffers);

// Order APIs
router.post('/customer/createOrder', authorization, MobileOrderController.createOrder);
router.post('/customer/getOrders', authorization, MobileOrderController.getOrders);
router.post('/customer/getOrder', authorization, MobileOrderController.getSingleOrder);
router.post('/customer/getOngoingOrders', authorization, MobileOrderController.getOngoingOrders);
router.post('/customer/cancelOrder', authorization, MobileOrderController.cancelOrder);

// ============= PAYMENT MANAGEMENT =============
// Payment APIs
router.post('/initiatePayment', authorization, MobilePaymentController.initiatePayment);
router.post('/capturePayment', authorization, MobilePaymentController.capturePayment);
router.post('/getPaymentStatus', authorization, MobilePaymentController.getPaymentStatus);

module.exports = router;
